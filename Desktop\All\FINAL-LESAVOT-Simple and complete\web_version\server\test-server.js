/**
 * Simple test server to isolate Magic Link issues
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '..')));

// Test Magic Link routes - commented out to isolate issue
// const magicLinkRoutes = require('./routes/magicLink');
// app.use('/api/auth', magicLinkRoutes);

// Health check
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', message: 'Test server running' });
});

// Error handling
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ 
        status: 'error', 
        message: err.message || 'Internal server error' 
    });
});

app.listen(PORT, () => {
    console.log(`Test server running on port ${PORT}`);
});
