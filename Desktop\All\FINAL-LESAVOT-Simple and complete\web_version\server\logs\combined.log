{"level":"info","message":"Server running on port 3000","service":"lesavot-api","timestamp":"2025-05-19T08:53:28.119Z"}
{"level":"info","message":"Server running on port 3000","service":"lesavot-api","timestamp":"2025-05-19T08:56:10.237Z"}
{"level":"info","message":"Connecting to MongoDB...","service":"lesavot-api","timestamp":"2025-06-27 19:30:11:3011"}
{"cause":{"beforeHandshake":false,"errorLabelSet":{}},"errorLabelSet":{},"level":"error","message":"Failed to connect to MongoDB: 145F0100:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\rec_layer_s3.c:1590:SSL alert number 80\n","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{"ac-eti5dcf-shard-00-00.sh5pgu3.mongodb.net:27017":{"$clusterTime":null,"address":"ac-eti5dcf-shard-00-00.sh5pgu3.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":1345738340,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-eti5dcf-shard-00-01.sh5pgu3.mongodb.net:27017":{"$clusterTime":null,"address":"ac-eti5dcf-shard-00-01.sh5pgu3.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":1345738268,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"},"ac-eti5dcf-shard-00-02.sh5pgu3.mongodb.net:27017":{"$clusterTime":null,"address":"ac-eti5dcf-shard-00-02.sh5pgu3.mongodb.net:27017","arbiters":[],"electionId":null,"error":{"beforeHandshake":false,"errorLabelSet":{}},"hosts":[],"iscryptd":false,"lastUpdateTime":1345738319,"lastWriteDate":0,"logicalSessionTimeoutMinutes":null,"maxBsonObjectSize":null,"maxMessageSizeBytes":null,"maxWireVersion":0,"maxWriteBatchSize":null,"me":null,"minRoundTripTime":0,"minWireVersion":0,"passives":[],"primary":null,"roundTripTime":-1,"setName":null,"setVersion":null,"tags":{},"topologyVersion":null,"type":"Unknown"}},"setName":"atlas-l0qihv-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"lesavot-api","stack":"MongoServerSelectionError: 145F0100:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error:c:\\ws\\deps\\openssl\\openssl\\ssl\\record\\rec_layer_s3.c:1590:SSL alert number 80\n\n    at Topology.selectServer (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\sdam\\topology.js:326:38)\n    at async Topology._connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\sdam\\topology.js:200:28)\n    at async Topology.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\sdam\\topology.js:152:13)\n    at async topologyConnect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\mongo_client.js:246:17)\n    at async MongoClient._connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\mongo_client.js:259:13)\n    at async MongoClient.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\mongodb\\lib\\mongo_client.js:184:13)\n    at async Database.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:46:7)\n    at async testMongoDBConnection (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-mongodb.js:21:5)","timestamp":"2025-06-27 19:30:16:3016"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 19:54:03:543"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 19:54:05:545"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 19:54:08 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 19:54:05:545"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 19:54:05:545"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 19:54:05:545"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 19:54:12:5412"}
{"level":"info","message":"PostgreSQL connection pool closed","service":"lesavot-api","timestamp":"2025-06-27 19:54:24:5424"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 19:57:55:5755"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 19:57:57:5757"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 19:58:00 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 19:57:57:5757"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 19:57:57:5757"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 19:57:57:5757"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 19:58:05:585"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 19:58:05:585"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 19:58:12:5812"}
{"code":"23505","constraint":"users_username_key","detail":"Key (username)=(testuser1_1751050675560) already exists.","file":"nbtinsert.c","length":224,"level":"error","line":"666","message":"Error saving user: duplicate key value violates unique constraint \"users_username_key\"","name":"error","routine":"_bt_check_unique","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: duplicate key value violates unique constraint \"users_username_key\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async User.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:88:24)\n    at async testValidationAndEdgeCases (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-user-model.js:308:7)\n    at async runUserModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-user-model.js:52:5)","table":"users","timestamp":"2025-06-27 19:58:20:5820"}
{"code":"23505","constraint":"users_email_key","detail":"Key (email)=(<EMAIL>) already exists.","file":"nbtinsert.c","length":227,"level":"error","line":"666","message":"Error saving user: duplicate key value violates unique constraint \"users_email_key\"","name":"error","routine":"_bt_check_unique","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: duplicate key value violates unique constraint \"users_email_key\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async User.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:88:24)\n    at async testValidationAndEdgeCases (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-user-model.js:326:7)\n    at async runUserModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-user-model.js:52:5)","table":"users","timestamp":"2025-06-27 19:58:21:5821"}
{"level":"info","message":"PostgreSQL connection pool closed","service":"lesavot-api","timestamp":"2025-06-27 19:58:22:5822"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:03:16:316"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:03:18:318"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:03:21 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:03:18:318"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:03:18:318"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:03:18:318"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:03:26:326"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:03:26:326"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:03:33:333"}
{"code":"23503","constraint":"sessions_user_id_fkey","detail":"Key (user_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":254,"level":"error","line":"2608","message":"Error saving session: insert or update on table \"sessions\" violates foreign key constraint \"sessions_user_id_fkey\"","name":"error","routine":"ri_ReportViolation","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: insert or update on table \"sessions\" violates foreign key constraint \"sessions_user_id_fkey\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async Session.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\Session.js:52:24)\n    at async testSessionCreation (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-session-model.js:85:21)\n    at async runSessionModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-session-model.js:37:22)","table":"sessions","timestamp":"2025-06-27 20:03:34:334"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:04:20:420"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:04:22:422"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:04:26 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:04:22:422"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:04:22:422"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:04:22:422"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:04:29:429"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:04:29:429"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:04:36:436"}
{"code":"23503","constraint":"sessions_user_id_fkey","detail":"Key (user_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":254,"level":"error","line":"2608","message":"Error saving session: insert or update on table \"sessions\" violates foreign key constraint \"sessions_user_id_fkey\"","name":"error","routine":"ri_ReportViolation","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: insert or update on table \"sessions\" violates foreign key constraint \"sessions_user_id_fkey\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async Session.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\Session.js:52:24)\n    at async testSessionCreation (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-session-model.js:85:21)\n    at async runSessionModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-session-model.js:37:22)","table":"sessions","timestamp":"2025-06-27 20:04:37:437"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:06:53:653"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:06:55:655"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:06:58 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:06:55:655"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:06:55:655"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:06:55:655"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:07:02:72"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:07:02:72"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:08:15:815"}
{"level":"info","message":"Cleaned up 0 expired sessions","service":"lesavot-api","timestamp":"2025-06-27 20:08:30:830"}
{"level":"info","message":"PostgreSQL connection pool closed","service":"lesavot-api","timestamp":"2025-06-27 20:08:31:831"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:14:13:1413"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:14:15:1415"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:14:18 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:14:15:1415"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:14:15:1415"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:14:15:1415"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:14:22:1422"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:14:22:1422"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:14:32:1432"}
{"code":"42703","file":"parse_target.c","length":147,"level":"error","line":"1066","message":"Error saving steganography operation: column \"media_type\" of relation \"steganography_operations\" does not exist","name":"error","position":"88","routine":"checkInsertTargets","service":"lesavot-api","severity":"ERROR","stack":"error: column \"media_type\" of relation \"steganography_operations\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async SteganographyOperation.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\SteganographyOperation.js:66:24)\n    at async testOperationCreation (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:86:7)\n    at async runSteganographyOperationModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:45:5)","timestamp":"2025-06-27 20:14:33:1433"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:21:40:2140"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:21:42:2142"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:21:45 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:21:42:2142"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:21:42:2142"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:21:42:2142"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:21:49:2149"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:21:49:2149"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:21:56:2156"}
{"level":"error","message":"Error finding operation by ID: \"[object Object]\" is not valid JSON","service":"lesavot-api","stack":"SyntaxError: \"[object Object]\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at SteganographyOperation.findById (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\SteganographyOperation.js:103:46)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async testOperationLookupMethods (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:122:23)\n    at async runSteganographyOperationModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:44:5)","timestamp":"2025-06-27 20:21:58:2158"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:22:56:2256"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:22:58:2258"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:23:01 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:22:58:2258"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:22:58:2258"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:22:58:2258"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:23:06:236"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:23:06:236"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:23:13:2313"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:24:10:2410"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:24:13:2413"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:24:16 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:24:13:2413"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:24:13:2413"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:24:13:2413"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:24:20:2420"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:24:20:2420"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:24:31:2431"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:26:42:2642"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:26:44:2644"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:26:47 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:26:44:2644"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:26:44:2644"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:26:44:2644"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:26:51:2651"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:26:51:2651"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:26:58:2658"}
{"code":"42703","file":"parse_relation.c","length":110,"level":"error","line":"3722","message":"Error getting global stats: column \"media_type\" does not exist","name":"error","position":"17","routine":"errorMissingColumn","service":"lesavot-api","severity":"ERROR","stack":"error: column \"media_type\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async SteganographyOperation.getGlobalStats (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\SteganographyOperation.js:310:31)\n    at async testOperationStatistics (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:282:25)\n    at async runSteganographyOperationModelTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-steganography-model.js:47:5)","timestamp":"2025-06-27 20:27:05:275"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:29:19:2919"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:29:23:2923"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:29:25 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:29:23:2923"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:29:23:2923"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:29:23:2923"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:29:31:2931"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:29:31:2931"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:29:39:2939"}
{"level":"info","message":"Cleaned up 0 old operations","service":"lesavot-api","timestamp":"2025-06-27 20:29:47:2947"}
{"level":"info","message":"PostgreSQL connection pool closed","service":"lesavot-api","timestamp":"2025-06-27 20:29:50:2950"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:39:36:3936"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:39:39:3939"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:39:42 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:39:39:3939"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:39:39:3939"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:39:39:3939"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:39:48:3948"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:39:48:3948"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:39:56:3956"}
{"code":"23503","constraint":"metrics_user_id_fkey","detail":"Key (user_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":250,"level":"error","line":"2608","message":"Error saving metrics: insert or update on table \"metrics\" violates foreign key constraint \"metrics_user_id_fkey\"","name":"error","routine":"ri_ReportViolation","schema":"public","service":"lesavot-api","severity":"ERROR","stack":"error: insert or update on table \"metrics\" violates foreign key constraint \"metrics_user_id_fkey\"\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async Metrics.save (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\Metrics.js:46:24)\n    at async Metrics.recordPageView (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\Metrics.js:87:7)\n    at async testMetricsModel (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-metrics-model.js:18:22)","table":"metrics","timestamp":"2025-06-27 20:39:56:3956"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:43:14:4314"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:43:17:4317"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:43:20 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:43:17:4317"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:43:17:4317"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:43:17:4317"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:43:23:4323"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:43:23:4323"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:43:31:4331"}
{"level":"info","message":"Cleaned up 0 old metrics","service":"lesavot-api","timestamp":"2025-06-27 20:43:36:4336"}
{"level":"info","message":"PostgreSQL connection pool closed","service":"lesavot-api","timestamp":"2025-06-27 20:43:36:4336"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:52:55:5255"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:52:58:5258"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:53:01 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:52:58:5258"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:52:58:5258"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:52:58:5258"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:53:05:535"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:53:05:535"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:53:13:5313"}
{"code":"42703","file":"parse_relation.c","length":121,"level":"error","line":"3722","message":"Error finding user by username: column \"password_reset_token\" does not exist","name":"error","position":"157","routine":"errorMissingColumn","service":"lesavot-api","severity":"ERROR","stack":"error: column \"password_reset_token\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg\\lib\\client.js:545:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:212:22)\n    at async User.findByUsername (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:120:22)\n    at async testAuthController (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-auth-controller.js:39:25)","timestamp":"2025-06-27 20:53:15:5315"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:53:43:5343"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:53:46:5346"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:53:49 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:53:46:5346"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:53:46:5346"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:53:46:5346"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:53:56:5356"}
{"level":"info","message":"PostgreSQL connection pool closed","service":"lesavot-api","timestamp":"2025-06-27 20:53:57:5357"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:54:07:547"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:54:10:5410"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:54:13 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:54:10:5410"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:54:10:5410"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:54:10:5410"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:54:17:5417"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:54:17:5417"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:54:24:5424"}
{"level":"info","message":"PostgreSQL connection pool closed","service":"lesavot-api","timestamp":"2025-06-27 20:54:36:5436"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:55:54:5554"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:55:56:5556"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:55:59 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:55:56:5556"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:55:56:5556"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:55:56:5556"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:56:03:563"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:56:03:563"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:56:11:5611"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:56:39:5639"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:56:41:5641"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:56:44 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:56:41:5641"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:56:41:5641"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:56:41:5641"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:56:49:5649"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:56:49:5649"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:56:57:5657"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 20:58:45:5845"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 20:58:48:5848"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 20:58:50 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 20:58:48:5848"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 20:58:48:5848"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:58:48:5848"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:58:57:5857"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 20:58:57:5857"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 20:59:07:597"}
{"level":"info","message":"Cleaned up 0 old metrics","service":"lesavot-api","timestamp":"2025-06-27 20:59:20:5920"}
{"level":"info","message":"PostgreSQL connection pool closed","service":"lesavot-api","timestamp":"2025-06-27 20:59:25:5925"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-27 21:00:17:017"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 21:00:17:017"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:00:18:018"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 21:00:19:019"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 21:00:22 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 21:00:19:019"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 21:00:19:019"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 21:00:19:019"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:00:27:027"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:00:27:027"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-27 21:00:27:027"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-27 21:00:27:027"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-27 21:00:27:027"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-27 21:05:53:553"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 21:05:53:553"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:05:55:555"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 21:05:56:556"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 21:05:59 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 21:05:56:556"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 21:05:56:556"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 21:05:56:556"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:06:05:65"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:06:05:65"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-27 21:06:05:65"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-27 21:06:05:65"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-27 21:06:05:65"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:09:06:96","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:09:09 +0000] \"GET /api/health HTTP/1.1\" 200 863 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:09:09:99"}
{"level":"warn","message":"Slow request: GET /api/health took 2794ms","service":"lesavot-api","timestamp":"2025-06-27 21:09:09:99"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:09:10:910","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:09:13 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 247 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:09:13:913"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 2250ms","service":"lesavot-api","timestamp":"2025-06-27 21:09:13:913"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:09:13:913","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:09:13:913","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:09:13 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 806 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:09:13:913"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:09:14:914","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:09:14 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 501 98 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:09:14:914"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:09:14:914","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:09:14 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 501 98 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:09:14:914"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:09:15:915","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:09:15 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 501 98 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:09:15:915"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:09:16:916","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:09:16 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 37 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:09:16:916"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:14:36:1436","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:14:38 +0000] \"GET /api/health HTTP/1.1\" 200 863 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:14:38:1438"}
{"level":"warn","message":"Slow request: GET /api/health took 2456ms","service":"lesavot-api","timestamp":"2025-06-27 21:14:38:1438"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:14:40:1440","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:14:42 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 247 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:14:42:1442"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1994ms","service":"lesavot-api","timestamp":"2025-06-27 21:14:42:1442"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:14:42:1442","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:14:42:1442","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:14:42 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 806 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:14:42:1442"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:14:43:1443","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:131:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:14:44:1444","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:14:44 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:14:44:1444"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/verify-otp took 1150ms","service":"lesavot-api","timestamp":"2025-06-27 21:14:44:1444"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:14:45:1445","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:14:45 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 501 98 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:14:45:1445"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:14:45:1445","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:14:45 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 501 98 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:14:45:1445"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:14:46:1446","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:14:46 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 501 98 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:14:46:1446"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:14:47:1447","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:14:47 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 37 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:14:47:1447"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-27 21:15:18:1518"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 21:15:18:1518"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:15:19:1519"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 21:15:20:1520"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 21:15:23 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 21:15:20:1520"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 21:15:20:1520"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 21:15:20:1520"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:15:28:1528"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:15:28:1528"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-27 21:15:28:1528"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-27 21:15:28:1528"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-27 21:15:28:1528"}
{"error":"jwt malformed","level":"warn","message":"Invalid token:","service":"lesavot-api","timestamp":"2025-06-27 21:16:41:1641"}
{"level":"error","message":"Error finding user by ID: Database not connected","service":"lesavot-api","stack":"Error: Database not connected\n    at Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:207:13)\n    at User.findById (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:162:37)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:42:36)\n    at testAuthMiddleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-auth-middleware.js:92:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:16:41:1641"}
{"level":"error","message":"Authentication middleware error: Database not connected","service":"lesavot-api","stack":"Error: Database not connected\n    at Database.query (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:207:13)\n    at User.findById (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\User.js:162:37)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:42:36)\n    at testAuthMiddleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-auth-middleware.js:92:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:16:41:1641"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:16:51:1651","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:16:54 +0000] \"GET /api/health HTTP/1.1\" 200 863 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:16:54:1654"}
{"level":"warn","message":"Slow request: GET /api/health took 2862ms","service":"lesavot-api","timestamp":"2025-06-27 21:16:54:1654"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:16:55:1655","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:16:57 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:16:57:1657"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 2018ms","service":"lesavot-api","timestamp":"2025-06-27 21:16:57:1657"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:16:58:1658","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e52bd5sm3589026f8f.59 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e52bd5sm3589026f8f.59 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e52bd5sm3589026f8f.59 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:17:00:170"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:17:00:170","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:17:00 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 500 334 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:17:00:170"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 2408ms","service":"lesavot-api","timestamp":"2025-06-27 21:17:00:170"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:17:01:171","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:17:02:172","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:17:02 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:17:02:172"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:17:02:172","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:17:03 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 401 85 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:17:03:173"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:17:03:173","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:17:03 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 401 85 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:17:03:173"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:17:04:174","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:17:04 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 401 85 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:17:04:174"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:17:05:175","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:17:05 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 405 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:17:05:175"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:17:06:176","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:17:06 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:17:06:176"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:19:37:1937","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:39 +0000] \"GET /api/health HTTP/1.1\" 200 863 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:39:1939"}
{"level":"warn","message":"Slow request: GET /api/health took 2458ms","service":"lesavot-api","timestamp":"2025-06-27 21:19:39:1939"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:19:41:1941","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:42 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:42:1942"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1517ms","service":"lesavot-api","timestamp":"2025-06-27 21:19:42:1942"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:19:43:1943","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538234b1b9sm88644525e9.11 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538234b1b9sm88644525e9.11 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538234b1b9sm88644525e9.11 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:19:45:1945"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:19:45:1945","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:45 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 500 334 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:45:1945"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 2176ms","service":"lesavot-api","timestamp":"2025-06-27 21:19:45:1945"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:19:45:1945","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:19:46:1946","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:46 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:46:1946"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:19:47:1947","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:47 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 401 85 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:47:1947"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:19:48:1948","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:48 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 401 85 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:48:1948"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:19:48:1948","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:49 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 401 85 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:49:1949"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:19:49:1949","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:50 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 405 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:50:1950"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:19:50:1950","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:19:50 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:19:50:1950"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-27 21:20:08:208"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 21:20:08:208"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:20:09:209"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 21:20:10:2010"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 21:20:13 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 21:20:10:2010"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 21:20:10:2010"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 21:20:10:2010"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:20:17:2017"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:20:17:2017"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-27 21:20:17:2017"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-27 21:20:17:2017"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-27 21:20:17:2017"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:20:30:2030","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:30 +0000] \"GET /api/health HTTP/1.1\" 200 863 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:30:2030"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:20:32:2032","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:34 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:34:2034"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1738ms","service":"lesavot-api","timestamp":"2025-06-27 21:20:34:2034"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:20:34:2034","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e5f8b6sm3628804f8f.91 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e5f8b6sm3628804f8f.91 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials ffacd0b85a97d-3a892e5f8b6sm3628804f8f.91 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:20:36:2036"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:20:36:2036","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:36 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 500 334 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:36:2036"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 2130ms","service":"lesavot-api","timestamp":"2025-06-27 21:20:36:2036"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:20:37:2037","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:20:38:2038","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:38 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:38:2038"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:20:38:2038","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:39 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 206 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:39:2039"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:20:39:2039","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Save operation not implemented. Database logic required.","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Save operation not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:20:40:2040","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:40 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 501 - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:40:2040"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:20:40:2040","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Get history not implemented. Database logic required.","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Get history not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:17:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:20:41:2041","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:41 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 501 - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:41:2041"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:20:41:2041","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:41 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 420 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:41:2041"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:20:42:2042","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:20:42 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:20:42:2042"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:21:29:2129","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:31 +0000] \"GET /api/health HTTP/1.1\" 200 864 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:31:2131"}
{"level":"warn","message":"Slow request: GET /api/health took 2605ms","service":"lesavot-api","timestamp":"2025-06-27 21:21:31:2131"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:21:33:2133","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:34 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:34:2134"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1515ms","service":"lesavot-api","timestamp":"2025-06-27 21:21:34:2134"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:21:35:2135","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-453823b6d50sm91865785e9.30 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-453823b6d50sm91865785e9.30 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-453823b6d50sm91865785e9.30 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:21:37:2137"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:21:37:2137","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:37 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 500 334 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:37:2137"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 2056ms","service":"lesavot-api","timestamp":"2025-06-27 21:21:37:2137"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:21:37:2137","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:21:38:2138","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:38 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:38:2138"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:21:39:2139","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:39 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 206 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:39:2139"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:21:40:2140","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Save operation not implemented. Database logic required.","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Save operation not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:21:40:2140","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:40 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 501 - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:40:2140"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:21:41:2141","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Get history not implemented. Database logic required.","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Get history not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:17:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:21:41:2141","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:41 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 501 - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:41:2141"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:21:42:2142","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:42 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 420 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:42:2142"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:21:42:2142","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:21:42 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:21:42:2142"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-27 21:22:18:2218"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 21:22:18:2218"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:22:19:2219"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 21:22:20:2220"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 21:22:23 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 21:22:20:2220"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 21:22:20:2220"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 21:22:20:2220"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:22:27:2227"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:22:27:2227"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-27 21:22:27:2227"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-27 21:22:27:2227"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-27 21:22:27:2227"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:22:40:2240","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:41 +0000] \"GET /api/health HTTP/1.1\" 200 864 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:41:2241"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:22:42:2242","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:44 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:44:2244"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1614ms","service":"lesavot-api","timestamp":"2025-06-27 21:22:44:2244"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:22:44:2244","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"Failed to send OTP email: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538a3b3213sm59112605e9.18 - gsmtp","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538a3b3213sm59112605e9.18 - gsmtp","responseCode":535,"service":"lesavot-api","stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 5b1f17b1804b1-4538a3b3213sm59112605e9.18 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-06-27 21:22:46:2246"}
{"ip":"::1","level":"error","message":"Error: Failed to send OTP. Please try again.","method":"POST","path":"/api/v1/auth/login","service":"lesavot-api","stack":"Error: Failed to send OTP. Please try again.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:22:46:2246","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:46 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 500 334 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:46:2246"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 2123ms","service":"lesavot-api","timestamp":"2025-06-27 21:22:46:2246"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:22:47:2247","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:133:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:22:48:2248","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:48 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:48:2248"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:22:48:2248","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:49 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 221 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:49:2249"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:22:49:2249","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Save operation not implemented. Database logic required.","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Save operation not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:12:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:22:50:2250","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:50 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 501 - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:50:2250"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:22:50:2250","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Get history not implemented. Database logic required.","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Get history not implemented. Database logic required.\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:17:15\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:22:50:2250","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:50 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 501 - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:50:2250"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:22:51:2251","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:51 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 420 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:51:2251"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:22:52:2252","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:22:52 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:22:52:2252"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:26:12:2612"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:26:23:2623"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:26:44:2644"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:28:26:2826"}
{"level":"info","message":"Email transporter configured successfully","service":"lesavot-api","timestamp":"2025-06-27 21:31:11:3111"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-27 21:31:12:3112"}
{"level":"warn","message":"Email transporter configuration error: EMAIL_USER and EMAIL_PASSWORD not set","service":"lesavot-api","timestamp":"2025-06-27 21:31:48:3148"}
{"level":"warn","message":"Email transporter configuration error: EMAIL_USER and EMAIL_PASSWORD not set","service":"lesavot-api","timestamp":"2025-06-27 21:34:07:347"}
{"level":"warn","message":"Email transporter configuration error: EMAIL_USER and EMAIL_PASSWORD not set","service":"lesavot-api","timestamp":"2025-06-27 21:34:29:3429"}
{"level":"warn","message":"Email transporter configuration error: EMAIL_USER and EMAIL_PASSWORD not set","service":"lesavot-api","timestamp":"2025-06-27 21:34:57:3457"}
{"level":"warn","message":"Email transporter configuration error: EMAIL_USER and EMAIL_PASSWORD not set","service":"lesavot-api","timestamp":"2025-06-27 21:39:52:3952"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-27 21:39:52:3952"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 21:39:52:3952"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:39:54:3954"}
{"level":"info","message":"PostgreSQL connection pool initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:39:54:3954"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 21:39:54:3954"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 21:39:57 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 21:39:54:3954"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 21:39:54:3954"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 21:39:54:3954"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:40:00:400"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:40:00:400"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-27 21:40:00:400"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-27 21:40:00:400"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-27 21:40:00:400"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:40:21:4021","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:22 +0000] \"GET /api/health HTTP/1.1\" 200 864 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:22:4022"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:40:23:4023","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:40:24:4024"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:25 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:25:4025"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1619ms","service":"lesavot-api","timestamp":"2025-06-27 21:40:25:4025"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:40:26:4026","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, skipping OTP email","service":"lesavot-api","timestamp":"2025-06-27 21:40:27:4027"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:27 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 97 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:27:4027"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 1067ms","service":"lesavot-api","timestamp":"2025-06-27 21:40:27:4027"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:40:27:4027","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:40:28:4028","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:28 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:28:4028"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:40:28:4028","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:29 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 221 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:29:4029"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:40:30:4030","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid mode. Must be encrypt or decrypt","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Invalid mode. Must be encrypt or decrypt\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:30:17\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\errorHandler.js:75:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:76:5)","timestamp":"2025-06-27 21:40:30:4030","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:30 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 400 - \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:30:4030"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:40:30:4030","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:40:32:4032"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error fetching steganography history: relation \"stego_history\" does not exist","name":"error","position":"86","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:94:20","timestamp":"2025-06-27 21:40:33:4033"}
{"ip":"::1","level":"error","message":"Error: Error fetching history","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error fetching history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:125:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:40:33:4033","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:33 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 500 305 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:33:4033"}
{"level":"warn","message":"Slow request: GET /api/v1/steganography/history took 2474ms","service":"lesavot-api","timestamp":"2025-06-27 21:40:33:4033"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:40:33:4033"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:40:33:4033","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:34 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 420 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:34:4034"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:40:34:4034","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:40:34 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:40:34:4034"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:41:45:4145","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:41:48 +0000] \"GET /api/health HTTP/1.1\" 200 864 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:41:48:4148"}
{"level":"warn","message":"Slow request: GET /api/health took 3220ms","service":"lesavot-api","timestamp":"2025-06-27 21:41:48:4148"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:41:50:4150","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:41:52 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:41:52:4152"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1686ms","service":"lesavot-api","timestamp":"2025-06-27 21:41:52:4152"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:41:52:4152","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, skipping OTP email","service":"lesavot-api","timestamp":"2025-06-27 21:41:53:4153"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:41:53 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 97 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:41:53:4153"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 1009ms","service":"lesavot-api","timestamp":"2025-06-27 21:41:53:4153"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:41:54:4154","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:41:55:4155","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:41:55 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:41:55:4155"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:41:55:4155","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:41:56 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 221 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:41:56:4156"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:41:56:4156","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:41:59:4159"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error saving steganography operation: relation \"stego_history\" does not exist","name":"error","position":"20","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:48:20","timestamp":"2025-06-27 21:41:59:4159"}
{"ip":"::1","level":"error","message":"Error: Error saving operation to history","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error saving operation to history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:62:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:41:59:4159","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:41:59 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 500 326 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:41:59:4159"}
{"level":"warn","message":"Slow request: POST /api/v1/steganography/history took 2542ms","service":"lesavot-api","timestamp":"2025-06-27 21:41:59:4159"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:41:59:4159"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:41:59:4159","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:42:01:421"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error fetching steganography history: relation \"stego_history\" does not exist","name":"error","position":"86","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:94:20","timestamp":"2025-06-27 21:42:01:421"}
{"ip":"::1","level":"error","message":"Error: Error fetching history","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error fetching history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:125:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:42:01:421","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:42:01 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 500 305 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:42:01:421"}
{"level":"warn","message":"Slow request: GET /api/v1/steganography/history took 2092ms","service":"lesavot-api","timestamp":"2025-06-27 21:42:01:421"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:42:02:422"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:42:02:422","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:42:02 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 420 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:42:02:422"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:42:03:423","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:42:03 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:42:03:423"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:46:19:4619","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:21 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:21:4621"}
{"level":"warn","message":"Slow request: GET /api/health took 2681ms","service":"lesavot-api","timestamp":"2025-06-27 21:46:21:4621"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:46:23:4623","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:25 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:25:4625"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1587ms","service":"lesavot-api","timestamp":"2025-06-27 21:46:25:4625"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:46:25:4625","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, skipping OTP email","service":"lesavot-api","timestamp":"2025-06-27 21:46:26:4626"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:26 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 97 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:26:4626"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:46:27:4627","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:46:27:4627","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:27 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:27:4627"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:46:28:4628","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:29 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 221 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:29:4629"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:46:29:4629","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:46:31:4631"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error saving steganography operation: relation \"stego_history\" does not exist","name":"error","position":"20","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:48:20","timestamp":"2025-06-27 21:46:31:4631"}
{"ip":"::1","level":"error","message":"Error: Error saving operation to history","method":"POST","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error saving operation to history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:62:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:46:31:4631","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:31 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 500 326 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:31:4631"}
{"level":"warn","message":"Slow request: POST /api/v1/steganography/history took 2301ms","service":"lesavot-api","timestamp":"2025-06-27 21:46:31:4631"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:46:32:4632"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:46:32:4632","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:46:34:4634"}
{"code":"42P01","file":"parse_relation.c","length":112,"level":"error","line":"1449","message":"Error fetching steganography history: relation \"stego_history\" does not exist","name":"error","position":"86","routine":"parserOpenTable","service":"lesavot-api","severity":"ERROR","stack":"error: relation \"stego_history\" does not exist\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:94:20","timestamp":"2025-06-27 21:46:34:4634"}
{"ip":"::1","level":"error","message":"Error: Error fetching history","method":"GET","path":"/api/v1/steganography/history","service":"lesavot-api","stack":"Error: Error fetching history\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\stegoController.js:125:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:46:34:4634","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:34 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 500 305 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:34:4634"}
{"level":"warn","message":"Slow request: GET /api/v1/steganography/history took 2180ms","service":"lesavot-api","timestamp":"2025-06-27 21:46:34:4634"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:46:34:4634"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:46:35:4635","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:35 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 420 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:35:4635"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:46:35:4635","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:46:35 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:46:35:4635"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"lesavot-api","timestamp":"2025-06-27 21:46:50:4650"}
{"level":"warn","message":"Email transporter configuration error: EMAIL_USER and EMAIL_PASSWORD not set","service":"lesavot-api","timestamp":"2025-06-27 21:46:59:4659"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-27 21:46:59:4659"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-27 21:46:59:4659"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:47:00:470"}
{"level":"info","message":"PostgreSQL connection pool initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:47:00:470"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-27 21:47:01:471"}
{"level":"info","message":"Connection test - Time: Fri Jun 27 2025 21:47:04 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-27 21:47:01:471"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-27 21:47:01:471"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-27 21:47:01:471"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:47:07:477"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-27 21:47:07:477"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-27 21:47:07:477"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-27 21:47:07:477"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-27 21:47:07:477"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:47:20:4720","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:21 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:21:4721"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:47:22:4722","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:24 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:24:4724"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1541ms","service":"lesavot-api","timestamp":"2025-06-27 21:47:24:4724"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:47:25:4725","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, skipping OTP email","service":"lesavot-api","timestamp":"2025-06-27 21:47:26:4726"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:26 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 97 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:26:4726"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 1043ms","service":"lesavot-api","timestamp":"2025-06-27 21:47:26:4726"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:47:26:4726","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:47:27:4727","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:27 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:27:4727"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:47:27:4727","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:28 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 221 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:28:4728"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:47:29:4729","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"info","message":"Steganography operation saved: 17 for user 31","service":"lesavot-api","timestamp":"2025-06-27 21:47:29:4729"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:29 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 201 404 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:29:4729"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:47:30:4730","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"info","message":"Retrieved 1 history entries for user 31","service":"lesavot-api","timestamp":"2025-06-27 21:47:31:4731"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:31 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 200 447 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:31:4731"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:47:31:4731","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:31 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 420 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:31:4731"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:47:32:4732","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:47:32 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:47:32:4732"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:48:01:481"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-27 21:48:40:4840","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:43 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:43:4843"}
{"level":"warn","message":"Slow request: GET /api/health took 2535ms","service":"lesavot-api","timestamp":"2025-06-27 21:48:43:4843"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-27 21:48:44:4844","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:46 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:46:4846"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1538ms","service":"lesavot-api","timestamp":"2025-06-27 21:48:46:4846"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-27 21:48:46:4846","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, skipping OTP email","service":"lesavot-api","timestamp":"2025-06-27 21:48:47:4847"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:47 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 97 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:47:4847"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 1056ms","service":"lesavot-api","timestamp":"2025-06-27 21:48:47:4847"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-27 21:48:48:4848","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:156:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-27 21:48:49:4849","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:49 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:49:4849"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-27 21:48:49:4849","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:50 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 200 221 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:50:4850"}
{"ip":"::1","level":"info","message":"POST /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:48:50:4850","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:48:52:4852"}
{"level":"info","message":"Steganography operation saved: 18 for user 32","service":"lesavot-api","timestamp":"2025-06-27 21:48:52:4852"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:52 +0000] \"POST /api/v1/steganography/history HTTP/1.1\" 201 404 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:52:4852"}
{"level":"warn","message":"Slow request: POST /api/v1/steganography/history took 2028ms","service":"lesavot-api","timestamp":"2025-06-27 21:48:52:4852"}
{"ip":"::1","level":"info","message":"GET /api/v1/steganography/history","service":"lesavot-api","timestamp":"2025-06-27 21:48:53:4853","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"info","message":"Retrieved 1 history entries for user 32","service":"lesavot-api","timestamp":"2025-06-27 21:48:54:4854"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:54 +0000] \"GET /api/v1/steganography/history HTTP/1.1\" 200 447 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:54:4854"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/refresh-token","service":"lesavot-api","timestamp":"2025-06-27 21:48:54:4854","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:54 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 420 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:54:4854"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/logout","service":"lesavot-api","timestamp":"2025-06-27 21:48:55:4855","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [27/Jun/2025:20:48:55 +0000] \"POST /api/v1/auth/logout HTTP/1.1\" 200 52 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-27 21:48:55:4855"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-27 21:49:24:4924"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"lesavot-api","timestamp":"2025-06-29 02:25:48:2548"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-30 07:06:17:617"}
{"cause":{},"level":"error","message":"Failed to connect to PostgreSQL: Connection terminated due to connection timeout","service":"lesavot-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:51:22)\n    at async testDatabaseConnection (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-postgresql.js:76:5)\n    at async runTests (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\test-postgresql.js:31:5)","timestamp":"2025-06-30 07:06:27:627"}
{"level":"info","message":"Email transporter configured successfully","service":"lesavot-api","timestamp":"2025-06-30 07:06:58:658"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-30 07:06:58:658"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-30 07:06:58:658"}
{"level":"warn","message":"Email transporter configuration error:","service":"lesavot-api","timestamp":"2025-06-30 07:06:59:659"}
{"cause":{},"level":"error","message":"Failed to initialize PostgreSQL connection pool: Connection terminated due to connection timeout","service":"lesavot-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-30 07:07:08:78"}
{"cause":{},"level":"error","message":"Failed to connect to PostgreSQL: Connection terminated due to connection timeout","service":"lesavot-api","stack":"Error: Connection terminated due to connection timeout\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\pg-pool\\index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Database.connect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\database.js:51:22)\n    at async initializeDatabase (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:116:5)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\server.js:301:5)","timestamp":"2025-06-30 07:07:08:78"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-06-30 07:24:44:2444"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-30 07:24:45:2445"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-30 07:24:45:2445"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-30 07:24:47:2447"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-30 07:24:47:2447"}
{"level":"info","message":"Connection test - Time: Mon Jun 30 2025 07:24:47 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-30 07:24:47:2447"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-30 07:24:47:2447"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-30 07:24:47:2447"}
{"level":"info","message":"PostgreSQL connection pool initialized successfully","service":"lesavot-api","timestamp":"2025-06-30 07:24:47:2447"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-30 07:24:54:2454"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-30 07:24:54:2454"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-30 07:24:54:2454"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-30 07:24:54:2454"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-30 07:24:54:2454"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-30 07:25:18:2518"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-30 07:26:20:2620","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:26:24 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:26:24:2624"}
{"level":"warn","message":"Slow request: GET /api/health took 4550ms","service":"lesavot-api","timestamp":"2025-06-30 07:26:24:2624"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-30 07:26:24:2624","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:26:26 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:26:26:2626"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1724ms","service":"lesavot-api","timestamp":"2025-06-30 07:26:26:2626"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-30 07:26:26:2626","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-06-30 07:26:27:2627"}
{"level":"info","message":"OTP for user testuser_1751264784593: 276183 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-06-30 07:26:27:2627"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:26:27 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:26:27:2627"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 1054ms","service":"lesavot-api","timestamp":"2025-06-30 07:26:27:2627"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-30 07:26:59:2659","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:27:05 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 200 668 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:27:05:275"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/verify-otp took 5991ms","service":"lesavot-api","timestamp":"2025-06-30 07:27:05:275"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"lesavot-api","timestamp":"2025-06-30 07:48:42:4842"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-06-30 07:53:17:5317"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-06-30 07:53:18:5318"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-06-30 07:53:18:5318"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-30 07:53:33:5333"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-06-30 07:53:41:5341"}
{"level":"info","message":"Connection test - Time: Mon Jun 30 2025 07:53:41 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-06-30 07:53:41:5341"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-06-30 07:53:41:5341"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-06-30 07:53:41:5341"}
{"level":"info","message":"PostgreSQL connection pool initialized successfully","service":"lesavot-api","timestamp":"2025-06-30 07:53:41:5341"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-06-30 07:54:18:5418"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-06-30 07:54:49:5449"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-06-30 07:54:49:5449"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-06-30 07:54:49:5449"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-06-30 07:54:49:5449"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-06-30 07:54:49:5449"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-30 07:56:34:5634","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:56:42 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:56:42:5642"}
{"level":"warn","message":"Slow request: GET /api/health took 8639ms","service":"lesavot-api","timestamp":"2025-06-30 07:56:42:5642"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-30 07:56:43:5643","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:56:45 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:56:45:5645"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 2135ms","service":"lesavot-api","timestamp":"2025-06-30 07:56:45:5645"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-30 07:56:45:5645","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-06-30 07:56:46:5646"}
{"level":"info","message":"OTP for user testuser_1751266603013: 127946 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-06-30 07:56:46:5646"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:56:46 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:56:46:5646"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 1297ms","service":"lesavot-api","timestamp":"2025-06-30 07:56:46:5646"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-30 07:57:19:5719","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/v1/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-30 07:57:26:5726","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:57:26 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:57:26:5726"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/verify-otp took 7607ms","service":"lesavot-api","timestamp":"2025-06-30 07:57:26:5726"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-30 07:59:02:592","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:59:09 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:59:09:599"}
{"level":"warn","message":"Slow request: GET /api/health took 6351ms","service":"lesavot-api","timestamp":"2025-06-30 07:59:09:599"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-30 07:59:09:599","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:59:12 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:59:12:5912"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 3464ms","service":"lesavot-api","timestamp":"2025-06-30 07:59:12:5912"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-30 07:59:12:5912","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-06-30 07:59:13:5913"}
{"level":"info","message":"OTP for user testuser_1751266742688: 865279 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-06-30 07:59:13:5913"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:59:13 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:59:13:5913"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 1045ms","service":"lesavot-api","timestamp":"2025-06-30 07:59:13:5913"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-30 07:59:13:5913","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:59:18 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 200 668 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:59:18:5918"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/verify-otp took 4898ms","service":"lesavot-api","timestamp":"2025-06-30 07:59:18:5918"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-30 07:59:18:5918","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Authentication middleware error: session.updateLastActivity is not a function","service":"lesavot-api","stack":"TypeError: session.updateLastActivity is not a function\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:70:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-30 07:59:19:5919"}
{"level":"http","message":"::1 - - [30/Jun/2025:06:59:19 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 500 73 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 07:59:19:5919"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-06-30 08:04:19:419","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:07:04:22 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 08:04:22:422"}
{"level":"warn","message":"Slow request: GET /api/health took 3757ms","service":"lesavot-api","timestamp":"2025-06-30 08:04:22:422"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/signup","service":"lesavot-api","timestamp":"2025-06-30 08:04:23:423","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:07:04:24 +0000] \"POST /api/v1/auth/signup HTTP/1.1\" 201 642 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 08:04:24:424"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/signup took 1618ms","service":"lesavot-api","timestamp":"2025-06-30 08:04:24:424"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/login","service":"lesavot-api","timestamp":"2025-06-30 08:04:24:424","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-06-30 08:04:26:426"}
{"level":"info","message":"OTP for user testuser_1751267059177: 568887 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-06-30 08:04:26:426"}
{"level":"http","message":"::1 - - [30/Jun/2025:07:04:26 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 08:04:26:426"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/login took 1774ms","service":"lesavot-api","timestamp":"2025-06-30 08:04:26:426"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/verify-otp","service":"lesavot-api","timestamp":"2025-06-30 08:04:26:426","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [30/Jun/2025:07:04:28 +0000] \"POST /api/v1/auth/verify-otp HTTP/1.1\" 200 668 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 08:04:28:428"}
{"level":"warn","message":"Slow request: POST /api/v1/auth/verify-otp took 2090ms","service":"lesavot-api","timestamp":"2025-06-30 08:04:28:428"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/me","service":"lesavot-api","timestamp":"2025-06-30 08:04:28:428","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Authentication middleware error: session.updateLastActivity is not a function","service":"lesavot-api","stack":"TypeError: session.updateLastActivity is not a function\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:70:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-30 08:04:29:429"}
{"level":"http","message":"::1 - - [30/Jun/2025:07:04:29 +0000] \"GET /api/v1/auth/me HTTP/1.1\" 500 73 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-06-30 08:04:29:429"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-02 03:17:55:1755"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-02 03:17:55:1755"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-02 03:17:55:1755"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-07-02 03:17:58:1758"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-02 03:17:58:1758"}
{"level":"info","message":"Connection test - Time: Wed Jul 02 2025 03:17:58 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-02 03:17:58:1758"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-02 03:17:58:1758"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-02 03:17:58:1758"}
{"level":"info","message":"PostgreSQL connection pool initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:17:58:1758"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:18:05:185"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:18:05:185"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-02 03:18:05:185"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-02 03:18:05:185"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-02 03:18:05:185"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-07-02 03:18:28:1828"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-02 03:18:36:1836"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-02 03:18:36:1836"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-02 03:18:36:1836"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-07-02 03:18:38:1838"}
{"level":"info","message":"PostgreSQL connection pool initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:18:38:1838"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-02 03:18:38:1838"}
{"level":"info","message":"Connection test - Time: Wed Jul 02 2025 03:18:39 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-02 03:18:38:1838"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-02 03:18:38:1838"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-02 03:18:38:1838"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:18:46:1846"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-02 03:20:44:2044"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-02 03:20:45:2045"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-02 03:20:45:2045"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-07-02 03:20:46:2046"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-02 03:20:47:2047"}
{"level":"info","message":"Connection test - Time: Wed Jul 02 2025 03:20:47 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-02 03:20:47:2047"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-02 03:20:47:2047"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-02 03:20:47:2047"}
{"level":"info","message":"PostgreSQL connection pool initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:20:47:2047"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:20:54:2054"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:20:54:2054"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-02 03:20:54:2054"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-02 03:20:54:2054"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-02 03:20:54:2054"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-07-02 03:21:17:2117"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/register","service":"lesavot-api","timestamp":"2025-07-02 03:21:21:2121","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:21:21 +0000] \"POST /api/v1/auth/register HTTP/1.1\" 401 82 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:21:21:2121"}
{"ip":"::1","level":"info","message":"GET /api/v1/health","service":"lesavot-api","timestamp":"2025-07-02 03:21:33:2133","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:21:33 +0000] \"GET /api/v1/health HTTP/1.1\" 404 56 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-02 03:21:33:2133"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-02 03:22:01:221","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:22:04 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-02 03:22:04:224"}
{"level":"warn","message":"Slow request: GET /api/health took 2889ms","service":"lesavot-api","timestamp":"2025-07-02 03:22:04:224"}
{"ip":"::1","level":"info","message":"POST /api/auth/register","service":"lesavot-api","timestamp":"2025-07-02 03:22:48:2248","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:22:48 +0000] \"POST /api/auth/register HTTP/1.1\" 401 82 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:22:48:2248"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:23:24:2324","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:23:27 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 639 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:23:27:2327"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3431ms","service":"lesavot-api","timestamp":"2025-07-02 03:23:27:2327"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:23:27:2327","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:23:28:2328"}
{"level":"info","message":"OTP for user otptest1751423004275: 830222 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:23:28:2328"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:23:28 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:23:28:2328"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1157ms","service":"lesavot-api","timestamp":"2025-07-02 03:23:28:2328"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:23:28:2328","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:23:29:2329","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:23:29 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:23:29:2329"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:24:42:2442","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:24:46 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 639 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:24:46:2446"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3684ms","service":"lesavot-api","timestamp":"2025-07-02 03:24:46:2446"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:24:46:2446","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:24:47:2447"}
{"level":"info","message":"OTP for user otptest1751423082795: 196228 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:24:47:2447"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:24:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:24:47:2447"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1309ms","service":"lesavot-api","timestamp":"2025-07-02 03:24:47:2447"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:24:47:2447","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:24:49:2449","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:24:49 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:24:49:2449"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 1129ms","service":"lesavot-api","timestamp":"2025-07-02 03:24:49:2449"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:24:49:2449","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:24:51 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 666 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:24:51:2451"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 2089ms","service":"lesavot-api","timestamp":"2025-07-02 03:24:51:2451"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:24:51:2451","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:24:52:2452","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:24:52 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:24:52:2452"}
{"ip":"::1","level":"info","message":"GET /api/auth/profile","service":"lesavot-api","timestamp":"2025-07-02 03:24:52:2452","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Authentication middleware error: session.updateLastActivity is not a function","service":"lesavot-api","stack":"TypeError: session.updateLastActivity is not a function\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:70:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:24:52:2452"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:24:52 +0000] \"GET /api/auth/profile HTTP/1.1\" 500 73 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:24:52:2452"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:25:36:2536","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:25:40 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 639 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:25:40:2540"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3431ms","service":"lesavot-api","timestamp":"2025-07-02 03:25:40:2540"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:25:40:2540","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:25:41:2541"}
{"level":"info","message":"OTP for user otptest1751423136881: 427816 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:25:41:2541"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:25:41 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:25:41:2541"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1483ms","service":"lesavot-api","timestamp":"2025-07-02 03:25:41:2541"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:25:41:2541","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:25:42:2542","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:25:42 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:25:42:2542"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:25:42:2542","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:25:45 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 666 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:25:45:2545"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 2516ms","service":"lesavot-api","timestamp":"2025-07-02 03:25:45:2545"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:25:45:2545","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:25:46:2546","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:25:46 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:25:46:2546"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"lesavot-api","timestamp":"2025-07-02 03:25:46:2546","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"error","message":"Authentication middleware error: session.updateLastActivity is not a function","service":"lesavot-api","stack":"TypeError: session.updateLastActivity is not a function\n    at exports.protect (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\auth.js:70:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:25:46:2546"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:25:46 +0000] \"GET /api/auth/me HTTP/1.1\" 500 73 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:25:46:2546"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:35:02:352","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:35:06 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 639 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:35:06:356"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 4060ms","service":"lesavot-api","timestamp":"2025-07-02 03:35:06:356"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:35:06:356","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"warn","message":"Rate limit exceeded","method":"POST","path":"/login","service":"lesavot-api","timestamp":"2025-07-02 03:35:06:356","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Too many authentication attempts, please try again later","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Too many authentication attempts, please try again later\n    at Object.defaultHandler [as handler] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\rateLimiter.js:41:12)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-02 03:35:06:356","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:35:06 +0000] \"POST /api/auth/login HTTP/1.1\" 429 703 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:35:06:356"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-02 03:35:23:3523","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:35:24 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-02 03:35:24:3524"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:36:03:363","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:36:07 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 639 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:36:07:367"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3724ms","service":"lesavot-api","timestamp":"2025-07-02 03:36:07:367"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:36:07:367","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"warn","message":"Rate limit exceeded","method":"POST","path":"/login","service":"lesavot-api","timestamp":"2025-07-02 03:36:07:367","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Too many authentication attempts, please try again later","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Too many authentication attempts, please try again later\n    at Object.defaultHandler [as handler] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\rateLimiter.js:41:12)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-02 03:36:07:367","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:36:07 +0000] \"POST /api/auth/login HTTP/1.1\" 429 703 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:36:07:367"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:37:13:3713","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:37:16 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 646 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:37:16:3716"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3671ms","service":"lesavot-api","timestamp":"2025-07-02 03:37:16:3716"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:37:16:3716","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:37:16:3716","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:37:16 +0000] \"POST /api/auth/login HTTP/1.1\" 400 806 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:37:16:3716"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:38:14:3814","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:38:18 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 646 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:38:18:3818"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3679ms","service":"lesavot-api","timestamp":"2025-07-02 03:38:18:3818"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:38:18:3818","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"warn","message":"Rate limit exceeded","method":"POST","path":"/login","service":"lesavot-api","timestamp":"2025-07-02 03:38:18:3818","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Too many authentication attempts, please try again later","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Too many authentication attempts, please try again later\n    at Object.defaultHandler [as handler] (C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\rateLimiter.js:41:12)\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-02 03:38:18:3818","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:38:18 +0000] \"POST /api/auth/login HTTP/1.1\" 429 703 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:38:18:3818"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-02 03:39:00:390"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-02 03:39:01:391"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-02 03:39:01:391"}
{"level":"debug","message":"New client connected to PostgreSQL pool","service":"lesavot-api","timestamp":"2025-07-02 03:39:02:392"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-02 03:39:03:393"}
{"level":"info","message":"Connection test - Time: Wed Jul 02 2025 03:39:03 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-02 03:39:03:393"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-02 03:39:03:393"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-02 03:39:03:393"}
{"level":"info","message":"PostgreSQL connection pool initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:39:03:393"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:39:10:3910"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 03:39:10:3910"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-02 03:39:10:3910"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-02 03:39:10:3910"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-02 03:39:10:3910"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-02 03:39:26:3926","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:39:27 +0000] \"GET /api/health HTTP/1.1\" 200 865 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-02 03:39:27:3927"}
{"level":"warn","message":"Slow request: GET /api/health took 1046ms","service":"lesavot-api","timestamp":"2025-07-02 03:39:27:3927"}
{"level":"debug","message":"Client removed from PostgreSQL pool","service":"lesavot-api","timestamp":"2025-07-02 03:39:33:3933"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:39:51:3951","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:39:52 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 646 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:39:52:3952"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 1646ms","service":"lesavot-api","timestamp":"2025-07-02 03:39:52:3952"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:39:52:3952","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:39:54:3954"}
{"level":"info","message":"OTP for user basictest1751423991130: 174271 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:39:54:3954"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:39:54 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:39:54:3954"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1130ms","service":"lesavot-api","timestamp":"2025-07-02 03:39:54:3954"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:39:54:3954","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:39:55:3955","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:39:55 +0000] \"POST /api/auth/login HTTP/1.1\" 401 316 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:39:55:3955"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1504ms","service":"lesavot-api","timestamp":"2025-07-02 03:39:55:3955"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:40:06:406","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:40:08 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 639 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:40:08:408"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 1786ms","service":"lesavot-api","timestamp":"2025-07-02 03:40:08:408"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:40:08:408","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:40:09:409"}
{"level":"info","message":"OTP for user otptest1751424006218: 222211 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:40:09:409"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:40:09 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:40:09:409"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1105ms","service":"lesavot-api","timestamp":"2025-07-02 03:40:09:409"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:40:09:409","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:40:10:4010","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:40:10 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:40:10:4010"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 1014ms","service":"lesavot-api","timestamp":"2025-07-02 03:40:10:4010"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:40:10:4010","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:40:12 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 666 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:40:12:4012"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 2344ms","service":"lesavot-api","timestamp":"2025-07-02 03:40:12:4012"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:40:12:4012","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:40:13:4013","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:40:13 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:40:13:4013"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 1122ms","service":"lesavot-api","timestamp":"2025-07-02 03:40:13:4013"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"lesavot-api","timestamp":"2025-07-02 03:40:13:4013","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:40:15 +0000] \"GET /api/auth/me HTTP/1.1\" 200 243 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:40:15:4015"}
{"level":"warn","message":"Slow request: GET /api/auth/me took 1630ms","service":"lesavot-api","timestamp":"2025-07-02 03:40:15:4015"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:41:12:4112","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:41:16 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 656 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:41:16:4116"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3692ms","service":"lesavot-api","timestamp":"2025-07-02 03:41:16:4116"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:41:16:4116","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:41:17:4117"}
{"level":"info","message":"OTP for user completetest1751424072623: 823406 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:41:17:4117"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:41:17 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:41:17:4117"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1011ms","service":"lesavot-api","timestamp":"2025-07-02 03:41:17:4117"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:41:17:4117","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:41:18:4118","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:41:18 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:41:18:4118"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 1022ms","service":"lesavot-api","timestamp":"2025-07-02 03:41:18:4118"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:41:18:4118","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:41:20 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 682 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:41:20:4120"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 2141ms","service":"lesavot-api","timestamp":"2025-07-02 03:41:20:4120"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"lesavot-api","timestamp":"2025-07-02 03:41:20:4120","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:41:22 +0000] \"GET /api/auth/me HTTP/1.1\" 200 253 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:41:22:4122"}
{"level":"warn","message":"Slow request: GET /api/auth/me took 1519ms","service":"lesavot-api","timestamp":"2025-07-02 03:41:22:4122"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:41:22:4122","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:41:23:4123","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:41:23 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:41:23:4123"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:41:23:4123","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:41:24:4124","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:41:24 +0000] \"POST /api/auth/login HTTP/1.1\" 401 316 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:41:24:4124"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1427ms","service":"lesavot-api","timestamp":"2025-07-02 03:41:24:4124"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-02 03:45:01:451","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:45:04 +0000] \"GET /api/health HTTP/1.1\" 200 866 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-02 03:45:04:454"}
{"level":"warn","message":"Slow request: GET /api/health took 2796ms","service":"lesavot-api","timestamp":"2025-07-02 03:45:04:454"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:45:27:4527","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:45:28 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 656 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:45:28:4528"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 1686ms","service":"lesavot-api","timestamp":"2025-07-02 03:45:28:4528"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:45:28:4528","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:45:30:4530"}
{"level":"info","message":"OTP for user completetest1751424327235: 553336 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:45:30:4530"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:45:30 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:45:30:4530"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1111ms","service":"lesavot-api","timestamp":"2025-07-02 03:45:30:4530"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:45:30:4530","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:45:31:4531","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:45:31 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:45:31:4531"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:45:31:4531","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:45:33 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 682 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:45:33:4533"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 2263ms","service":"lesavot-api","timestamp":"2025-07-02 03:45:33:4533"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"lesavot-api","timestamp":"2025-07-02 03:45:33:4533","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:45:34 +0000] \"GET /api/auth/me HTTP/1.1\" 200 253 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:45:34:4534"}
{"level":"warn","message":"Slow request: GET /api/auth/me took 1639ms","service":"lesavot-api","timestamp":"2025-07-02 03:45:34:4534"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:45:34:4534","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:45:35:4535","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:45:35 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:45:35:4535"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 1013ms","service":"lesavot-api","timestamp":"2025-07-02 03:45:35:4535"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:45:35:4535","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:45:37:4537","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:45:37 +0000] \"POST /api/auth/login HTTP/1.1\" 401 316 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:45:37:4537"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1377ms","service":"lesavot-api","timestamp":"2025-07-02 03:45:37:4537"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-02 03:46:23:4623","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:46:26 +0000] \"GET /api/health HTTP/1.1\" 200 867 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:46:26:4626"}
{"level":"warn","message":"Slow request: GET /api/health took 2665ms","service":"lesavot-api","timestamp":"2025-07-02 03:46:26:4626"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:46:26:4626","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:46:28 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 643 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:46:28:4628"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 1627ms","service":"lesavot-api","timestamp":"2025-07-02 03:46:28:4628"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:46:28:4628","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:46:29:4629"}
{"level":"info","message":"OTP for user frontend1751424383692: 252422 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:46:29:4629"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:46:29 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:46:29:4629"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1058ms","service":"lesavot-api","timestamp":"2025-07-02 03:46:29:4629"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:46:29:4629","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:46:31 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 669 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:46:31:4631"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 2180ms","service":"lesavot-api","timestamp":"2025-07-02 03:46:31:4631"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"lesavot-api","timestamp":"2025-07-02 03:46:31:4631","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:46:32 +0000] \"GET /api/auth/me HTTP/1.1\" 200 245 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:46:32:4632"}
{"level":"warn","message":"Slow request: GET /api/auth/me took 1593ms","service":"lesavot-api","timestamp":"2025-07-02 03:46:32:4632"}
{"ip":"::1","level":"info","message":"POST /api/steganography/text/encrypt","service":"lesavot-api","timestamp":"2025-07-02 03:46:32:4632","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:46:33 +0000] \"POST /api/steganography/text/encrypt HTTP/1.1\" 404 74 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:46:33:4633"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:51:25:5125","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:29 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 656 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:29:5129"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3632ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:29:5129"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:51:29:5129","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:51:30:5130"}
{"level":"info","message":"OTP for user completetest1751424685307: 095196 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:51:30:5130"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:30 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:30:5130"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1271ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:30:5130"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:51:30:5130","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:51:31:5131","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:31 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:31:5131"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:51:31:5131","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:33 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 682 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:33:5133"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 2223ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:33:5133"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"lesavot-api","timestamp":"2025-07-02 03:51:33:5133","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:35 +0000] \"GET /api/auth/me HTTP/1.1\" 200 253 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:35:5135"}
{"level":"warn","message":"Slow request: GET /api/auth/me took 1737ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:35:5135"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:51:35:5135","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid or expired OTP","method":"POST","path":"/api/auth/verify-otp","service":"lesavot-api","stack":"Error: Invalid or expired OTP\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:186:17\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:51:36:5136","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:36 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 401 304 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:36:5136"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 1025ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:36:5136"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:51:36:5136","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:109:19\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-02 03:51:37:5137","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:37 +0000] \"POST /api/auth/login HTTP/1.1\" 401 316 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:37:5137"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1433ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:37:5137"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-02 03:51:49:5149","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:50 +0000] \"GET /api/health HTTP/1.1\" 200 867 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:50:5150"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-02 03:51:50:5150","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:52 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 643 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:52:5152"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 1740ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:52:5152"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-02 03:51:52:5152","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"warn","message":"Email transporter not configured, logging OTP instead","service":"lesavot-api","timestamp":"2025-07-02 03:51:53:5153"}
{"level":"info","message":"OTP for user frontend1751424709525: 008410 (expires in 5 minutes)","service":"lesavot-api","timestamp":"2025-07-02 03:51:53:5153"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:53 +0000] \"POST /api/auth/login HTTP/1.1\" 200 148 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:53:5153"}
{"level":"warn","message":"Slow request: POST /api/auth/login took 1223ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:53:5153"}
{"ip":"::1","level":"info","message":"POST /api/auth/verify-otp","service":"lesavot-api","timestamp":"2025-07-02 03:51:53:5153","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:55 +0000] \"POST /api/auth/verify-otp HTTP/1.1\" 200 669 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:55:5155"}
{"level":"warn","message":"Slow request: POST /api/auth/verify-otp took 2283ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:55:5155"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"lesavot-api","timestamp":"2025-07-02 03:51:55:5155","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:57 +0000] \"GET /api/auth/me HTTP/1.1\" 200 245 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:57:5157"}
{"level":"warn","message":"Slow request: GET /api/auth/me took 1669ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:57:5157"}
{"ip":"::1","level":"info","message":"POST /api/steganography/text/encrypt","service":"lesavot-api","timestamp":"2025-07-02 03:51:57:5157","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"}
{"level":"http","message":"::1 - - [02/Jul/2025:02:51:58 +0000] \"POST /api/steganography/text/encrypt HTTP/1.1\" 404 74 \"-\" \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\"","service":"lesavot-api","timestamp":"2025-07-02 03:51:58:5158"}
{"level":"warn","message":"Slow request: POST /api/steganography/text/encrypt took 1049ms","service":"lesavot-api","timestamp":"2025-07-02 03:51:58:5158"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-02 04:23:53:2353"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-02 04:23:53:2353"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-02 04:23:53:2353"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-02 04:23:56:2356"}
{"level":"info","message":"Connection test - Time: Wed Jul 02 2025 04:23:56 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-02 04:23:56:2356"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-02 04:23:56:2356"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-02 04:23:56:2356"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 04:24:04:244"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-02 04:24:04:244"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-02 04:24:04:244"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-02 04:24:04:244"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-02 04:24:04:244"}
{"ip":"::1","level":"info","message":"GET /health","service":"lesavot-api","timestamp":"2025-07-02 04:24:16:2416","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-02 04:24:46:2446","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Slow request: GET /api/health took 2608ms","service":"lesavot-api","timestamp":"2025-07-02 04:24:48:2448"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 03:43:39:4339"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 03:43:40:4340"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 03:43:40:4340"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 03:43:42:4342"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 03:43:43 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 03:43:42:4342"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 03:43:42:4342"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 03:43:42:4342"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 03:43:50:4350"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 03:43:50:4350"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 03:43:50:4350"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 03:43:50:4350"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 03:43:50:4350"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 03:44:25:4425"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 03:44:25:4425"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 03:44:25:4425"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 03:44:27:4427"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 03:44:28 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 03:44:27:4427"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 03:44:27:4427"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 03:44:27:4427"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 03:44:35:4435"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 03:44:35:4435"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 03:44:35:4435"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 03:44:35:4435"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 03:44:35:4435"}
{"level":"error","message":"Error generating JWT token: JWT signing key not configured for algorithm HS256","service":"lesavot-api","stack":"Error: JWT signing key not configured for algorithm HS256\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:72:13)\n    at [eval]:1:187\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","timestamp":"2025-07-04 03:45:14:4514"}
{"level":"error","message":"Error generating JWT token: JWT secret not configured for algorithm HS256","service":"lesavot-api","stack":"Error: JWT secret not configured for algorithm HS256\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:77:15)\n    at [eval]:1:148\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)\n    at node:internal/main/eval_string:51:3","timestamp":"2025-07-04 03:46:20:4620"}
{"level":"error","message":"Error generating JWT token: Bad \"options.audience\" option. The payload already has an \"aud\" property.","service":"lesavot-api","stack":"Error: Bad \"options.audience\" option. The payload already has an \"aud\" property.\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:221:24\n    at Array.forEach (<anonymous>)\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:217:35)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:85:23)\n    at [eval]:1:148\n    at runScriptInThisContext (node:internal/vm:209:10)\n    at node:internal/process/execution:118:14\n    at [eval]-wrapper:6:24\n    at runScript (node:internal/process/execution:101:62)\n    at evalScript (node:internal/process/execution:133:3)","timestamp":"2025-07-04 03:47:47:4747"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 03:48:02:482"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 03:48:02:482"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 03:48:02:482"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 03:48:04:484"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 03:48:05 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 03:48:04:484"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 03:48:04:484"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 03:48:04:484"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 03:48:13:4813"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 03:48:13:4813"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 03:48:13:4813"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 03:48:13:4813"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 03:48:13:4813"}
{"ip":"::1","level":"info","message":"GET /api/v1/health","service":"lesavot-api","timestamp":"2025-07-04 03:48:26:4826","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"GET /","service":"lesavot-api","timestamp":"2025-07-04 03:48:35:4835","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"GET /api/v1/auth/test","service":"lesavot-api","timestamp":"2025-07-04 03:48:43:4843","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"POST /api/v1/auth/register","service":"lesavot-api","timestamp":"2025-07-04 03:51:26:5126","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:01:18:118"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:01:18:118"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:01:18:118"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:01:20:120"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:01:21 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:01:20:120"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:01:20:120"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:01:20:120"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:01:29:129"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:01:29:129"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 04:01:29:129"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 04:01:29:129"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 04:01:29:129"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:01:42:142"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:01:42:142"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:01:42:142"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:01:44:144"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:01:45 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:01:44:144"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:01:44:144"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:01:44:144"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:01:52:152"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:02:08:28"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:02:08:28"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:02:08:28"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:02:10:210"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:02:11 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:02:10:210"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:02:10:210"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:02:10:210"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:02:19:219"}
{"level":"info","message":"OTP JWT token generated for email:","service":"lesavot-api","timestamp":"2025-07-04 04:04:13:413"}
{"level":"info","message":"OTP JWT token verified for email:","service":"lesavot-api","timestamp":"2025-07-04 04:04:13:413"}
{"error":"invalid token","level":"warn","message":"Invalid OTP token:","service":"lesavot-api","timestamp":"2025-07-04 04:04:13:413"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:04:47:447"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:04:47:447"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:04:47:447"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:04:49:449"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:04:50 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:04:49:449"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:04:49:449"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:04:49:449"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:04:57:457"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:15:35:1535"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:15:36:1536"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:15:36:1536"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:15:38:1538"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:15:39 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:15:38:1538"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:15:38:1538"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:15:38:1538"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:15:46:1546"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:15:57:1557"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:15:58:1558"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:15:58:1558"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:16:00:160"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:16:01 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:16:00:160"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:16:00:160"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:16:00:160"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:16:09:169"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:30:53:3053"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:30:54:3054"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:30:54:3054"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:30:56:3056"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:30:57 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:30:56:3056"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:30:56:3056"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:30:56:3056"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:31:05:315"}
{"ip":"::1","level":"info","message":"GET /api/v1/health","service":"lesavot-api","timestamp":"2025-07-04 04:31:08:318","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"GET /api/auth/test","service":"lesavot-api","timestamp":"2025-07-04 04:31:18:3118","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"GET /api/auth/test","service":"lesavot-api","timestamp":"2025-07-04 04:36:07:367","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 04:36:23:3623","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 04:36:23:3623","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:42:48:4248"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:42:48:4248"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:42:48:4248"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:42:51:4251"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:42:52 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:42:51:4251"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:42:51:4251"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:42:51:4251"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:42:59:4259"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:42:59:4259"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 04:42:59:4259"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 04:42:59:4259"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 04:42:59:4259"}
{"ip":"::1","level":"info","message":"POST /api/auth/forgot-password","service":"lesavot-api","timestamp":"2025-07-04 04:43:01:431","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 04:43:21:4321","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 04:43:21:4321","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 04:43:44:4344","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"info","message":"JWT token generated for user:","service":"lesavot-api","timestamp":"2025-07-04 04:43:47:4347"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 3130ms","service":"lesavot-api","timestamp":"2025-07-04 04:43:47:4347"}
{"ip":"::1","level":"info","message":"POST /api/auth/forgot-password","service":"lesavot-api","timestamp":"2025-07-04 04:43:55:4355","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Email transporter not configured, skipping password reset email","service":"lesavot-api","timestamp":"2025-07-04 04:43:56:4356"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-04 04:52:41:5241","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Slow request: GET /api/health took 2662ms","service":"lesavot-api","timestamp":"2025-07-04 04:52:43:5243"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 04:52:51:5251","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"info","message":"JWT token generated for user:","service":"lesavot-api","timestamp":"2025-07-04 04:52:53:5253"}
{"level":"warn","message":"Slow request: POST /api/auth/signup took 1557ms","service":"lesavot-api","timestamp":"2025-07-04 04:52:53:5253"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-04 04:53:01:531","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 04:53:02:532"}
{"level":"error","message":"Unhandled error: Failed to generate authentication token","method":"POST","path":"/login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 04:53:02:532"}
{"ip":"::1","level":"error","message":"Error: Failed to generate authentication token","method":"POST","path":"/api/auth/login","service":"lesavot-api","timestamp":"2025-07-04 04:53:02:532","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:53:51:5351","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:622:17","timestamp":"2025-07-04 04:53:53:5353"}
{"level":"error","message":"Unhandled error: Failed to generate authentication token","method":"POST","path":"/simple-login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:622:17","timestamp":"2025-07-04 04:53:53:5353"}
{"ip":"::1","level":"error","message":"Error: Failed to generate authentication token","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:53:53:5353","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Slow request: POST /api/auth/simple-login took 2286ms","service":"lesavot-api","timestamp":"2025-07-04 04:53:53:5353"}
{"ip":"::1","level":"info","message":"POST /api/auth/forgot-password","service":"lesavot-api","timestamp":"2025-07-04 04:54:09:549","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Email transporter not configured, skipping password reset email","service":"lesavot-api","timestamp":"2025-07-04 04:54:09:549"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:54:51:5451"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:54:51:5451"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:54:51:5451"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:54:53:5453"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:54:54 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:54:53:5453"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:54:53:5453"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:54:53:5453"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:55:01:551"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:55:01:551"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 04:55:01:551"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 04:55:01:551"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 04:55:01:551"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:55:05:555","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:622:17","timestamp":"2025-07-04 04:55:06:556"}
{"level":"error","message":"Unhandled error: Failed to generate authentication token","method":"POST","path":"/simple-login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:622:17","timestamp":"2025-07-04 04:55:06:556"}
{"ip":"::1","level":"error","message":"Error: Failed to generate authentication token","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:55:06:556","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-04 04:55:30:5530","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:58:26:5826"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:58:26:5826"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:58:26:5826"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:58:28:5828"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 04:58:29 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:58:28:5828"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:58:28:5828"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:58:28:5828"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:58:36:5836"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 04:58:36:5836"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 04:58:36:5836"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 04:58:36:5836"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 04:58:36:5836"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:58:43:5843","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 04:58:43:5843"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 04:58:43:5843"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 04:58:43:5843","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 04:59:56:5956"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 04:59:57:5957"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 04:59:57:5957"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 04:59:59:5959"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 05:00:00 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 04:59:59:5959"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 04:59:59:5959"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 04:59:59:5959"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 05:00:07:07"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 05:00:07:07"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 05:00:07:07"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 05:00:07:07"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 05:00:07:07"}
{"ip":"::1","level":"info","message":"POST /api/debug-user","service":"lesavot-api","timestamp":"2025-07-04 05:00:12:012","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 05:00:32:032"}
{"level":"warn","message":"Running in production without HTTPS. This is not recommended.","service":"lesavot-api","timestamp":"2025-07-04 05:00:32:032"}
{"level":"info","message":"Connecting to PostgreSQL...","service":"lesavot-api","timestamp":"2025-07-04 05:00:32:032"}
{"level":"info","message":"Successfully connected to PostgreSQL database: lesavotdb","service":"lesavot-api","timestamp":"2025-07-04 05:00:34:034"}
{"level":"info","message":"Connection test - Time: Fri Jul 04 2025 05:00:35 GMT+0100 (West Africa Standard Time)","service":"lesavot-api","timestamp":"2025-07-04 05:00:34:034"}
{"level":"info","message":"PostgreSQL version: PostgreSQL","service":"lesavot-api","timestamp":"2025-07-04 05:00:34:034"}
{"level":"info","message":"Initializing PostgreSQL schema...","service":"lesavot-api","timestamp":"2025-07-04 05:00:34:034"}
{"level":"info","message":"PostgreSQL schema and indexes initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 05:00:42:042"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 05:00:42:042"}
{"level":"info","message":"Server running on port 3000 in production mode","service":"lesavot-api","timestamp":"2025-07-04 05:00:42:042"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 05:00:42:042"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 05:00:42:042"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 05:54:52:5452"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 05:54:53:5453"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 05:54:53:5453"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 05:54:53:5453"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 05:54:53:5453"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 05:54:53:5453"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 05:54:53:5453"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 05:54:53:5453"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 05:54:53:5453"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"lesavot-api","timestamp":"2025-07-04 05:55:08:558","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:04:55:08 +0000] \"GET /api/health HTTP/1.1\" 200 219 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 05:55:08:558"}
{"ip":"::1","level":"info","message":"POST /api/debug-user","service":"lesavot-api","timestamp":"2025-07-04 05:55:21:5521","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:04:55:21 +0000] \"POST /api/debug-user HTTP/1.1\" 200 70 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 05:55:21:5521"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 05:55:30:5530","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"info","message":"JWT token generated for user:","service":"lesavot-api","timestamp":"2025-07-04 05:55:31:5531"}
{"level":"http","message":"::1 - - [04/Jul/2025:04:55:31 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 562 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 05:55:31:5531"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 05:55:44:5544","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 05:55:44:5544"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 05:55:44:5544"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:656:17","timestamp":"2025-07-04 05:55:44:5544","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:04:55:44 +0000] \"POST /api/auth/simple-login HTTP/1.1\" 500 246 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 05:55:44:5544"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"lesavot-api","timestamp":"2025-07-04 05:56:00:560","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 05:56:00:560"}
{"level":"error","message":"Unhandled error: Failed to generate authentication token","method":"POST","path":"/login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 05:56:00:560"}
{"ip":"::1","level":"error","message":"Error: Failed to generate authentication token","method":"POST","path":"/api/auth/login","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:128:19","timestamp":"2025-07-04 05:56:00:560","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:04:56:00 +0000] \"POST /api/auth/login HTTP/1.1\" 500 399 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 05:56:00:560"}
{"ip":"::1","level":"info","message":"POST /api/debug-user","service":"lesavot-api","timestamp":"2025-07-04 05:56:22:5622","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:04:56:22 +0000] \"POST /api/debug-user HTTP/1.1\" 200 75 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 05:56:22:5622"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 05:57:25:5725"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 05:57:43:5743","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 05:57:43:5743","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:04:57:43 +0000] \"POST /api/auth/signup HTTP/1.1\" 400 826 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 05:57:43:5743"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 05:57:55:5755","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"warn","message":"Simple login attempt with invalid password: testuser2","service":"lesavot-api","timestamp":"2025-07-04 05:57:56:5756"}
{"ip":"::1","level":"error","message":"Error: Invalid username or password","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Invalid username or password\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:623:19","timestamp":"2025-07-04 05:57:56:5756","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:04:57:56 +0000] \"POST /api/auth/simple-login HTTP/1.1\" 401 238 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 05:57:56:5756"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 05:58:21:5821"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 06:00:50:050","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 06:00:50:050","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:05:00:50 +0000] \"POST /api/auth/signup HTTP/1.1\" 400 826 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 06:00:50:050"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 06:02:33:233"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 06:02:34:234"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 06:02:34:234"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 06:02:34:234"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 06:02:34:234"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 06:02:34:234"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 06:02:34:234"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 06:02:34:234"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 06:02:34:234"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 06:02:53:253","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Validation failed","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Validation failed\n    at handleValidationErrors (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\middleware\\validators.js:27:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 06:02:53:253","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:05:02:53 +0000] \"POST /api/auth/signup HTTP/1.1\" 400 826 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 06:02:53:253"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 06:04:09:49"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 06:04:27:427","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"info","message":"JWT token generated for user:","service":"lesavot-api","timestamp":"2025-07-04 06:04:28:428"}
{"level":"http","message":"::1 - - [04/Jul/2025:05:04:28 +0000] \"POST /api/auth/signup HTTP/1.1\" 201 559 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 06:04:28:428"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 06:04:40:440","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 06:04:41:441"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:627:19","timestamp":"2025-07-04 06:04:41:441"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:656:17","timestamp":"2025-07-04 06:04:41:441","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:05:04:41 +0000] \"POST /api/auth/simple-login HTTP/1.1\" 500 246 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 06:04:41:441"}
{"ip":"::1","level":"info","message":"POST /api/debug-user","service":"lesavot-api","timestamp":"2025-07-04 07:17:15:1715","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:06:17:15 +0000] \"POST /api/debug-user HTTP/1.1\" 200 74 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 07:17:15:1715"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 07:19:16:1916"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 10:47:45:4745"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 10:47:46:4746"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 10:47:46:4746"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 10:47:46:4746"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 10:47:46:4746"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 10:47:46:4746"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 10:47:46:4746"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 10:47:46:4746"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 10:47:46:4746"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 10:48:02:482","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:48:03:483"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:48:03:483"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:669:17","timestamp":"2025-07-04 10:48:03:483","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:09:48:03 +0000] \"POST /api/auth/simple-login HTTP/1.1\" 500 246 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 10:48:03:483"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 10:51:15:5115"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 10:51:16:5116"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 10:51:16:5116"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 10:51:16:5116"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 10:51:16:5116"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 10:51:16:5116"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 10:51:16:5116"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 10:51:16:5116"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 10:51:16:5116"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 10:51:34:5134","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:51:35:5135"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:51:35:5135"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:669:17","timestamp":"2025-07-04 10:51:35:5135","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:09:51:35 +0000] \"POST /api/auth/simple-login HTTP/1.1\" 500 246 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 10:51:35:5135"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 10:51:46:5146","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Username already exists","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Username already exists\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:296:17","timestamp":"2025-07-04 10:51:46:5146","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:09:51:46 +0000] \"POST /api/auth/signup HTTP/1.1\" 409 228 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 10:51:46:5146"}
{"ip":"::1","level":"info","message":"POST /api/auth/signup","service":"lesavot-api","timestamp":"2025-07-04 10:51:56:5156","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"error","message":"Error: Username already exists","method":"POST","path":"/api/auth/signup","service":"lesavot-api","stack":"Error: Username already exists\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:296:17","timestamp":"2025-07-04 10:51:56:5156","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:09:51:56 +0000] \"POST /api/auth/signup HTTP/1.1\" 409 228 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 10:51:56:5156"}
{"ip":"::1","level":"info","message":"POST /api/debug-user","service":"lesavot-api","timestamp":"2025-07-04 10:52:06:526","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:09:52:06 +0000] \"POST /api/debug-user HTTP/1.1\" 200 75 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 10:52:06:526"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 10:53:08:538"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 10:53:47:5347"}
{"ip":"::1","level":"info","message":"POST /api/auth/simple-login","service":"lesavot-api","timestamp":"2025-07-04 10:54:05:545","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating JWT token: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60","service":"lesavot-api","stack":"Error: \"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60\n    at module.exports [as sign] (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\jsonwebtoken\\sign.js:213:22)\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:26:23)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:54:05:545"}
{"level":"error","message":"Simple login error: Failed to generate authentication token","service":"lesavot-api","stack":"Error: Failed to generate authentication token\n    at exports.generateToken (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\jwt.js:39:11)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:640:19","timestamp":"2025-07-04 10:54:05:545"}
{"ip":"::1","level":"error","message":"Error: Login failed due to server error","method":"POST","path":"/api/auth/simple-login","service":"lesavot-api","stack":"Error: Login failed due to server error\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\authController.js:670:17","timestamp":"2025-07-04 10:54:05:545","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:09:54:05 +0000] \"POST /api/auth/simple-login HTTP/1.1\" 500 246 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 10:54:05:545"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 10:55:44:5544"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 10:56:26:5626"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 11:00:08:08"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"warn","message":"SMTP configuration missing. Magic link emails will not be sent.","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 11:28:45:2845"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"warn","message":"SMTP configuration missing. Magic link emails will not be sent.","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 11:29:44:2944"}
{"ip":"::1","level":"info","message":"GET /auth.html","service":"lesavot-api","timestamp":"2025-07-04 11:30:07:307","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:30:07 +0000] \"GET /auth.html HTTP/1.1\" 404 52 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lesavot-api","timestamp":"2025-07-04 11:30:07:307"}
{"ip":"::1","level":"info","message":"GET /favicon.ico","service":"lesavot-api","timestamp":"2025-07-04 11:30:08:308","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:30:08 +0000] \"GET /favicon.ico HTTP/1.1\" 404 54 \"http://localhost:3000/auth.html\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lesavot-api","timestamp":"2025-07-04 11:30:08:308"}
{"ip":"::1","level":"info","message":"GET /auth.html","service":"lesavot-api","timestamp":"2025-07-04 11:30:48:3048","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:30:48 +0000] \"GET /auth.html HTTP/1.1\" 404 52 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lesavot-api","timestamp":"2025-07-04 11:30:48:3048"}
{"ip":"::1","level":"info","message":"GET /.well-known/appspecific/com.chrome.devtools.json","service":"lesavot-api","timestamp":"2025-07-04 11:30:48:3048","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:30:48 +0000] \"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1\" 404 91 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"lesavot-api","timestamp":"2025-07-04 11:30:48:3048"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 11:32:28:3228"}
{"level":"warn","message":"SMTP configuration missing. Magic link emails will not be sent.","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 11:32:29:3229"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 11:34:17:3417"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"warn","message":"SMTP configuration missing. Magic link emails will not be sent.","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 11:39:38:3938"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signup/request-magic-link","service":"lesavot-api","timestamp":"2025-07-04 11:42:01:421","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signup <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-04 11:42:01:421","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:42:01:421"}
{"email":"<EMAIL>","error":"User already exists with this email","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:42:01:421"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:42:01 +0000] \"POST /api/magic-auth/signup/request-magic-link HTTP/1.1\" 409 97 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 11:42:01:421"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signin/request-magic-link","service":"lesavot-api","timestamp":"2025-07-04 11:42:16:4216","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signin <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-04 11:42:16:4216","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: no such column: display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: no such column: display_name\n--> in Database#all('\\n' +\n  '          UPDATE users \\n' +\n  '          SET email = ?, display_name = ?, is_verified = ?, \\n' +\n  '              magic_link_token = ?, magic_link_expires = ?,\\n' +\n  '              failed_attempts = ?, account_locked_until = ?,\\n' +\n  '              last_login = ?, updated_at = CURRENT_TIMESTAMP\\n' +\n  '          WHERE id = ?\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  '',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.4Fusjtss7xDoc_Tw2eA3YJy-n2aw9uuyJRC7W5NIZBE',\n  2025-07-04T10:57:17.201Z,\n  0,\n  null,\n  null,\n  2\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:102:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:132:22)","timestamp":"2025-07-04 11:42:17:4217"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"0":"<EMAIL>","1":"","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.4Fusjtss7xDoc_Tw2eA3YJy-n2aw9uuyJRC7W5NIZBE","4":"2025-07-04T10:57:17.201Z","5":0,"6":null,"7":null,"8":2,"level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: no such column: display_name","ip":"::1","level":"error","message":"Signin magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"ip":"::1","level":"error","message":"Error: Failed to send magic link. Please try again.","method":"POST","path":"/api/magic-auth/signin/request-magic-link","service":"lesavot-api","stack":"Error: Failed to send magic link. Please try again.\n    at requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:160:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 11:42:17:4217","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:42:17 +0000] \"POST /api/magic-auth/signin/request-magic-link HTTP/1.1\" 500 383 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 11:42:17:4217"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signin/request-magic-link","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signin <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: no such column: display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: no such column: display_name\n--> in Database#all('\\n' +\n  '          UPDATE users \\n' +\n  '          SET email = ?, display_name = ?, is_verified = ?, \\n' +\n  '              magic_link_token = ?, magic_link_expires = ?,\\n' +\n  '              failed_attempts = ?, account_locked_until = ?,\\n' +\n  '              last_login = ?, updated_at = CURRENT_TIMESTAMP\\n' +\n  '          WHERE id = ?\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  '',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.GybRm7ak2l_pkmFUQ2oVIgO7GkQ92KL93QjtPsIS7no',\n  2025-07-04T10:58:34.208Z,\n  0,\n  null,\n  null,\n  2\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:102:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:132:22)","timestamp":"2025-07-04 11:43:34:4334"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"0":"<EMAIL>","1":"","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.GybRm7ak2l_pkmFUQ2oVIgO7GkQ92KL93QjtPsIS7no","4":"2025-07-04T10:58:34.208Z","5":0,"6":null,"7":null,"8":2,"level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: no such column: display_name","ip":"::1","level":"error","message":"Signin magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"ip":"::1","level":"error","message":"Error: Failed to send magic link. Please try again.","method":"POST","path":"/api/magic-auth/signin/request-magic-link","service":"lesavot-api","stack":"Error: Failed to send magic link. Please try again.\n    at requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:160:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 11:43:34:4334","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:43:34 +0000] \"POST /api/magic-auth/signin/request-magic-link HTTP/1.1\" 500 383 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 11:43:34:4334"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"warn","message":"SMTP configuration missing. Magic link emails will not be sent.","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 11:43:59:4359"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signin/request-magic-link","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signin <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: no such column: display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: no such column: display_name\n--> in Database#all('\\n' +\n  '          UPDATE users \\n' +\n  '          SET email = ?, display_name = ?, is_verified = ?, \\n' +\n  '              magic_link_token = ?, magic_link_expires = ?,\\n' +\n  '              failed_attempts = ?, account_locked_until = ?,\\n' +\n  '              last_login = ?, updated_at = CURRENT_TIMESTAMP\\n' +\n  '          WHERE id = ?\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  '',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.BcpkMp3m6_Blcmh-ZVQ0fuN9fD5Pq0cl4-DoqkWckxU',\n  2025-07-04T10:59:11.983Z,\n  0,\n  null,\n  null,\n  2\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:102:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:132:22)","timestamp":"2025-07-04 11:44:11:4411"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"0":"<EMAIL>","1":"","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************.BcpkMp3m6_Blcmh-ZVQ0fuN9fD5Pq0cl4-DoqkWckxU","4":"2025-07-04T10:59:11.983Z","5":0,"6":null,"7":null,"8":2,"level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: no such column: display_name","ip":"::1","level":"error","message":"Signin magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:44:11:4411"}
{"ip":"::1","level":"error","message":"Error: Failed to send magic link. Please try again.","method":"POST","path":"/api/magic-auth/signin/request-magic-link","service":"lesavot-api","stack":"Error: Failed to send magic link. Please try again.\n    at requestSigninMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:160:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 11:44:11:4411","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:44:12 +0000] \"POST /api/magic-auth/signin/request-magic-link HTTP/1.1\" 500 383 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 11:44:12:4412"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signup/request-magic-link","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signup <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: table users has no column named display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.rNb7nQUvub0CoG8dqyAXyEORH07CzG5k6CunZvsKNqs',\n  2025-07-04T10:59:36.079Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-04 11:44:36:4436"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"0":"<EMAIL>","1":"newuser","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.rNb7nQUvub0CoG8dqyAXyEORH07CzG5k6CunZvsKNqs","4":"2025-07-04T10:59:36.079Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: table users has no column named display_name","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"ip":"::1","level":"error","message":"Error: Failed to send magic link. Please try again.","method":"POST","path":"/api/magic-auth/signup/request-magic-link","service":"lesavot-api","stack":"Error: Failed to send magic link. Please try again.\n    at requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:97:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-04 11:44:36:4436","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"level":"http","message":"::1 - - [04/Jul/2025:10:44:36 +0000] \"POST /api/magic-auth/signup/request-magic-link HTTP/1.1\" 500 382 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-04 11:44:36:4436"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-04 12:43:18:4318"}
{"level":"warn","message":"SMTP configuration missing. Magic link emails will not be sent.","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-04 12:43:19:4319"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signup/request-magic-link","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signup <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: table users has no column named display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.SlUCWac0g79yiG10B8K2ZL5LT8xqrU2d72Rdv7RU7q4',\n  2025-07-06T05:39:00.170Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-06 06:24:00:240"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"0":"<EMAIL>","1":"newuser","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.SlUCWac0g79yiG10B8K2ZL5LT8xqrU2d72Rdv7RU7q4","4":"2025-07-06T05:39:00.170Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: table users has no column named display_name","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.SlUCWac0g79yiG10B8K2ZL5LT8xqrU2d72Rdv7RU7q4',\n  2025-07-06T05:39:00.170Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-06 06:24:00:240"}
{"level":"http","message":"::1 - - [06/Jul/2025:05:24:00 +0000] \"POST /api/magic-auth/signup/request-magic-link HTTP/1.1\" 500 1649 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-06 06:24:00:240"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"warn","message":"SMTP configuration missing. Magic link emails will not be sent.","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-07 10:59:59:5959"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signup/request-magic-link","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signup <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_ERROR","errno":1,"level":"error","message":"SQLite query error: SQLITE_ERROR: table users has no column named display_name","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o5xSfB3ZExRLVUrgY2zVLzBk5OVpLlAsycGola9YoFk',\n  2025-07-07T10:16:10.660Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:01:10:110"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"0":"<EMAIL>","1":"newuser","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o5xSfB3ZExRLVUrgY2zVLzBk5OVpLlAsycGola9YoFk","4":"2025-07-07T10:16:10.660Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"email":"<EMAIL>","error":"SQLITE_ERROR: table users has no column named display_name","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_ERROR: table users has no column named display_name\n--> in Database#all('\\n' +\n  '          INSERT INTO users (email, display_name, is_verified, magic_link_token, magic_link_expires)\\n' +\n  '          VALUES (?, ?, ?, ?, ?)\\n' +\n  '          RETURNING *\\n' +\n  '        ', [\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.o5xSfB3ZExRLVUrgY2zVLzBk5OVpLlAsycGola9YoFk',\n  2025-07-07T10:16:10.660Z\n], [Function (anonymous)])\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:123:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:110:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:01:10:110"}
{"level":"http","message":"::1 - - [07/Jul/2025:10:01:10 +0000] \"POST /api/magic-auth/signup/request-magic-link HTTP/1.1\" 500 1649 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-07 11:01:10:110"}
{"level":"warn","message":"Email transporter configuration: EMAIL_USER and EMAIL_PASSWORD not set - OTP emails will be logged instead","service":"lesavot-api","timestamp":"2025-07-07 11:03:51:351"}
{"level":"warn","message":"SMTP configuration missing. Magic link emails will not be sent.","service":"lesavot-api","timestamp":"2025-07-07 11:03:51:351"}
{"level":"info","message":"HTTP server created for development","service":"lesavot-api","timestamp":"2025-07-07 11:03:51:351"}
{"level":"info","message":"Connecting to SQLite database...","service":"lesavot-api","timestamp":"2025-07-07 11:03:51:351"}
{"level":"info","message":"SQLite database connected successfully","service":"lesavot-api","timestamp":"2025-07-07 11:03:51:351"}
{"level":"info","message":"Adding display_name column to users table","service":"lesavot-api","timestamp":"2025-07-07 11:03:51:351"}
{"level":"info","message":"Adding magic_link_token column to users table","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"Adding magic_link_expires column to users table","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"Adding failed_attempts column to users table","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"Adding is_verified column to users table","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"Database migrations completed","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"SQLite database tables initialized","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"Database initialized successfully","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"Server running on port 3000 in development mode","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"API version: v1","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"level":"info","message":"Database connection established","service":"lesavot-api","timestamp":"2025-07-07 11:03:52:352"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signup/request-magic-link","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signup <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_CONSTRAINT","errno":19,"level":"error","message":"SQLite query error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username\n--> in Statement#all([\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.Ml9Nf2jYadc8y7D-vNTNsumDlfK3w4dV3cO5R2URyeM',\n  2025-07-07T10:19:24.560Z\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:04:24:424"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"0":"<EMAIL>","1":"newuser","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.Ml9Nf2jYadc8y7D-vNTNsumDlfK3w4dV3cO5R2URyeM","4":"2025-07-07T10:19:24.560Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"email":"<EMAIL>","error":"SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username\n--> in Statement#all([\n  '<EMAIL>',\n  'newuser',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************.Ml9Nf2jYadc8y7D-vNTNsumDlfK3w4dV3cO5R2URyeM',\n  2025-07-07T10:19:24.560Z\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:04:24:424"}
{"level":"http","message":"::1 - - [07/Jul/2025:10:04:24 +0000] \"POST /api/magic-auth/signup/request-magic-link HTTP/1.1\" 500 1774 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-07 11:04:24:424"}
{"ip":"::1","level":"info","message":"POST /api/magic-auth/signup/request-magic-link","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"ip":"::1","level":"info","message":"Magic link signup <NAME_EMAIL>","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202"}
{"__augmented":true,"code":"SQLITE_CONSTRAINT","errno":19,"level":"error","message":"SQLite query error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username\n--> in Statement#all([\n  '<EMAIL>',\n  'newuser2',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.5a5o3rOu6kF0GTkAUk8_2XLpdy7AFxN___UPalogIc0',\n  2025-07-07T10:21:12.106Z\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:06:12:612"}
{"level":"error","message":"SQL:","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
{"0":"<EMAIL>","1":"newuser2","2":false,"3":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.5a5o3rOu6kF0GTkAUk8_2XLpdy7AFxN___UPalogIc0","4":"2025-07-07T10:21:12.106Z","level":"error","message":"Params:","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
{"level":"error","message":"Error saving user:","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
{"level":"error","message":"Error generating magic link:","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
{"email":"<EMAIL>","error":"SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username","ip":"::1","level":"error","message":"Signup magic link request failed:","service":"lesavot-api","stack":"Error: SQLITE_CONSTRAINT: NOT NULL constraint failed: users.username\n--> in Statement#all([\n  '<EMAIL>',\n  'newuser2',\n  false,\n  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************.5a5o3rOu6kF0GTkAUk8_2XLpdy7AFxN___UPalogIc0',\n  2025-07-07T10:21:12.106Z\n], [Function: replacement])\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:88:19)\n    at Database.<anonymous> (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\node_modules\\sqlite3\\lib\\sqlite3.js:20:19)\n    at C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:179:15\n    at new Promise (<anonymous>)\n    at SQLiteDatabase.query (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\utils\\sqlite-database.js:166:12)\n    at MagicLinkUser.save (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\models\\MagicLinkUser.js:120:39)\n    at MagicLinkService.generateMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\services\\magicLinkService.js:64:18)\n    at async requestSignupMagicLink (C:\\Users\\<USER>\\Desktop\\All\\FINAL-LESAVOT-Simple and complete\\web_version\\server\\controllers\\magicLinkController.js:69:22)","timestamp":"2025-07-07 11:06:12:612"}
{"level":"http","message":"::1 - - [07/Jul/2025:10:06:12 +0000] \"POST /api/magic-auth/signup/request-magic-link HTTP/1.1\" 500 1777 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4202\"","service":"lesavot-api","timestamp":"2025-07-07 11:06:12:612"}
