/**
 * Magic Link Service
 * 
 * Handles Magic Link generation, validation, and email delivery
 * for the LESAVOT authentication system.
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const logger = require('../utils/logger');
const MagicLinkUser = require('../models/MagicLinkUser');

class MagicLinkService {
  constructor() {
    this.emailTransporter = this.createEmailTransporter();
  }

  /**
   * Create email transporter for sending magic links
   */
  createEmailTransporter() {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
      logger.warn('SMTP configuration missing. Magic link emails will not be sent.');
      return null;
    }

    return nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
  }

  /**
   * Generate Magic Link for user authentication
   */
  async generateMagicLink(email, isSignup = false) {
    try {
      // Check if user exists
      let user = await MagicLinkUser.findByEmail(email);
      
      if (isSignup && user) {
        throw new Error('User already exists with this email');
      }
      
      if (!isSignup && !user) {
        throw new Error('No account found with this email');
      }

      // Create user if signup
      if (isSignup && !user) {
        user = new MagicLinkUser({ email });
      }

      // Generate magic link token
      const magicToken = user.generateMagicLinkToken();
      
      // Save user with magic token
      await user.save();

      // Create magic link URL
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      const magicLinkUrl = `${baseUrl}/auth/magic-link?token=${magicToken}&email=${encodeURIComponent(email)}`;

      logger.info(`Magic link generated for ${email}`, {
        email,
        isSignup,
        tokenExpires: user.magicLinkExpires
      });

      return {
        success: true,
        magicLinkUrl,
        token: magicToken,
        expiresAt: user.magicLinkExpires,
        user: user.toJSON()
      };

    } catch (error) {
      logger.error('Error generating magic link:', error.message);
      throw error;
    }
  }

  /**
   * Verify Magic Link token and authenticate user
   */
  async verifyMagicLink(token, email) {
    try {
      // Verify JWT token structure
      const tokenVerification = MagicLinkUser.verifyMagicLinkToken(token);
      
      if (!tokenVerification.valid) {
        throw new Error('Invalid or expired magic link');
      }

      // Verify email matches token
      if (tokenVerification.email !== email) {
        throw new Error('Magic link email mismatch');
      }

      // Find user by magic token
      const user = await MagicLinkUser.findByMagicToken(token);
      
      if (!user) {
        throw new Error('Magic link not found or expired');
      }

      // Check if account is locked
      if (user.isAccountLocked()) {
        throw new Error('Account is temporarily locked due to too many failed attempts');
      }

      // Clear magic token and mark as verified
      await user.clearMagicToken();
      await user.resetFailedAttempts();

      // Generate session JWT
      const sessionToken = this.generateSessionToken(user);

      logger.info(`Magic link verified successfully for ${email}`, {
        userId: user.id,
        email: user.email
      });

      return {
        success: true,
        user: user.toJSON(),
        sessionToken,
        message: 'Authentication successful'
      };

    } catch (error) {
      logger.error('Error verifying magic link:', error.message);
      
      // Increment failed attempts if user exists
      try {
        const user = await MagicLinkUser.findByEmail(email);
        if (user) {
          await user.incrementFailedAttempts();
        }
      } catch (incrementError) {
        logger.error('Error incrementing failed attempts:', incrementError.message);
      }

      throw error;
    }
  }

  /**
   * Generate session JWT token for authenticated user
   */
  generateSessionToken(user) {
    const payload = {
      userId: user.id,
      email: user.email,
      displayName: user.displayName,
      isVerified: user.isVerified,
      type: 'session'
    };

    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d',
      issuer: 'lesavot-auth',
      audience: 'lesavot-app'
    });
  }

  /**
   * Verify session JWT token
   */
  static verifySessionToken(token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET, {
        issuer: 'lesavot-auth',
        audience: 'lesavot-app'
      });

      return {
        valid: true,
        userId: decoded.userId,
        email: decoded.email,
        displayName: decoded.displayName,
        isVerified: decoded.isVerified
      };
    } catch (error) {
      logger.error('Session token verification failed:', error.message);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Send Magic Link via email
   */
  async sendMagicLinkEmail(email, magicLinkUrl, isSignup = false) {
    if (!this.emailTransporter) {
      // For development: log the magic link instead of sending email
      const actionText = isSignup ? 'Complete Signup' : 'Sign In';
      logger.warn(`Email transporter not configured. Magic link for ${email}:`);
      logger.warn(`Action: ${actionText}`);
      logger.warn(`Magic Link URL: ${magicLinkUrl}`);
      logger.warn('In production, this would be sent via email.');

      // Return success for development testing
      return {
        success: true,
        message: 'Magic link generated (logged to console for development)',
        developmentUrl: magicLinkUrl
      };
    }

    try {
      const subject = isSignup ? 'Welcome to LESAVOT - Complete Your Signup' : 'LESAVOT - Your Magic Link';
      const actionText = isSignup ? 'Complete Signup' : 'Sign In';
      
      const htmlContent = this.generateMagicLinkEmailHTML(email, magicLinkUrl, actionText, isSignup);
      const textContent = this.generateMagicLinkEmailText(email, magicLinkUrl, actionText, isSignup);

      const mailOptions = {
        from: `"LESAVOT Platform" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
        to: email,
        subject: subject,
        text: textContent,
        html: htmlContent
      };

      const result = await this.emailTransporter.sendMail(mailOptions);
      
      logger.info(`Magic link email sent successfully to ${email}`, {
        messageId: result.messageId,
        isSignup
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Magic link sent successfully'
      };

    } catch (error) {
      logger.error('Error sending magic link email:', error.message);
      throw new Error('Failed to send magic link email');
    }
  }

  /**
   * Generate HTML email template for magic link
   */
  generateMagicLinkEmailHTML(email, magicLinkUrl, actionText, isSignup) {
    const welcomeText = isSignup ? 
      'Welcome to LESAVOT! Click the button below to complete your account setup.' :
      'Click the button below to sign in to your LESAVOT account.';

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>LESAVOT - ${actionText}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #2c3e50; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 8px; }
            .button { display: inline-block; padding: 12px 30px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .button:hover { background: #2980b9; }
            .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🔐 LESAVOT</div>
                <p>Secure Steganography Platform</p>
            </div>
            
            <div class="content">
                <h2>Hello!</h2>
                <p>${welcomeText}</p>
                
                <div style="text-align: center;">
                    <a href="${magicLinkUrl}" class="button">${actionText}</a>
                </div>
                
                <div class="warning">
                    <strong>⚠️ Security Notice:</strong>
                    <ul>
                        <li>This link expires in 15 minutes</li>
                        <li>Only use this link if you requested it</li>
                        <li>Never share this link with anyone</li>
                    </ul>
                </div>
                
                <p>If the button doesn't work, copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 3px;">
                    ${magicLinkUrl}
                </p>
            </div>
            
            <div class="footer">
                <p>This email was sent to ${email}</p>
                <p>If you didn't request this, please ignore this email.</p>
                <p>&copy; 2024 LESAVOT Platform. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>`;
  }

  /**
   * Generate plain text email for magic link
   */
  generateMagicLinkEmailText(email, magicLinkUrl, actionText, isSignup) {
    const welcomeText = isSignup ? 
      'Welcome to LESAVOT! Use the link below to complete your account setup.' :
      'Use the link below to sign in to your LESAVOT account.';

    return `
LESAVOT - ${actionText}

Hello!

${welcomeText}

${actionText}: ${magicLinkUrl}

SECURITY NOTICE:
- This link expires in 15 minutes
- Only use this link if you requested it  
- Never share this link with anyone

This email was sent to ${email}
If you didn't request this, please ignore this email.

© 2024 LESAVOT Platform. All rights reserved.
    `.trim();
  }

  /**
   * Clean up expired magic link tokens
   */
  async cleanupExpiredTokens() {
    try {
      const query = `
        UPDATE users 
        SET magic_link_token = NULL, magic_link_expires = NULL
        WHERE magic_link_expires < CURRENT_TIMESTAMP
        AND magic_link_token IS NOT NULL
      `;
      
      const result = await database.query(query);
      
      if (result.rowCount > 0) {
        logger.info(`Cleaned up ${result.rowCount} expired magic link tokens`);
      }
      
      return result.rowCount;
    } catch (error) {
      logger.error('Error cleaning up expired tokens:', error.message);
      throw error;
    }
  }
}

module.exports = new MagicLinkService();
