/**
 * SQLite Database Connection and Utilities for Local Development
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const logger = require('./logger');

class SQLiteDatabase {
  constructor() {
    this.db = null;
    this.isConnected = false;
    this.dbPath = process.env.SQLITE_DB_PATH || './database/lesavot.db';
    
    // Ensure database directory exists
    const dbDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
  }

  /**
   * Connect to SQLite database
   */
  async connect() {
    return new Promise((resolve, reject) => {
      if (this.isConnected) {
        logger.info('SQLite database already connected');
        return resolve(this.db);
      }

      logger.info('Connecting to SQLite database...');
      
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          logger.error('SQLite connection error:', err);
          return reject(err);
        }
        
        this.isConnected = true;
        logger.info('SQLite database connected successfully');
        
        // Initialize tables
        this.initializeTables()
          .then(() => resolve(this.db))
          .catch(reject);
      });
    });
  }

  /**
   * Initialize database tables
   */
  async initializeTables() {
    const tables = [
      // Users table - Magic Link Authentication (email-only)
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        display_name TEXT,
        is_verified BOOLEAN DEFAULT FALSE,
        magic_link_token TEXT,
        magic_link_expires DATETIME,
        failed_attempts INTEGER DEFAULT 0,
        account_locked_until DATETIME,
        last_login DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Sessions table - Magic Link Authentication
      `CREATE TABLE IF NOT EXISTS sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT UNIQUE NOT NULL,
        user_id INTEGER,
        email TEXT,
        session_type TEXT DEFAULT 'magic_link',
        magic_token TEXT,
        is_verified BOOLEAN DEFAULT 0,
        expires_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`,
      
      // Metrics table
      `CREATE TABLE IF NOT EXISTS metrics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        metric_type TEXT,
        metric_name TEXT,
        metric_value TEXT,
        metadata TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`
    ];

    for (const tableSQL of tables) {
      await this.run(tableSQL);
    }
    
    logger.info('SQLite database tables initialized');
  }

  /**
   * Execute a query that returns rows
   */
  async query(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        return reject(new Error('Database not connected'));
      }

      // Convert PostgreSQL-style parameters ($1, $2) to SQLite-style (?)
      let convertedSql = sql;
      let convertedParams = params;

      if (sql.includes('$')) {
        convertedSql = sql.replace(/\$\d+/g, '?');
      }

      this.db.all(convertedSql, convertedParams, (err, rows) => {
        if (err) {
          logger.error('SQLite query error:', err);
          logger.error('SQL:', convertedSql);
          logger.error('Params:', convertedParams);
          return reject(err);
        }

        // Format response to match PostgreSQL format
        resolve({ rows: rows || [] });
      });
    });
  }

  /**
   * Execute a query that doesn't return rows (INSERT, UPDATE, DELETE)
   */
  async run(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        return reject(new Error('Database not connected'));
      }

      // Convert PostgreSQL-style parameters ($1, $2) to SQLite-style (?)
      let convertedSql = sql;
      let convertedParams = params;

      if (sql.includes('$')) {
        convertedSql = sql.replace(/\$\d+/g, '?');
      }

      this.db.run(convertedSql, convertedParams, function(err) {
        if (err) {
          logger.error('SQLite run error:', err);
          logger.error('SQL:', convertedSql);
          logger.error('Params:', convertedParams);
          return reject(err);
        }

        // Return result in PostgreSQL-like format
        resolve({
          rows: [],
          rowCount: this.changes,
          lastID: this.lastID
        });
      });
    });
  }

  /**
   * Check if database is connected
   */
  isConnectedToDatabase() {
    return this.isConnected;
  }

  /**
   * Get database statistics
   */
  async getStats() {
    try {
      const userCount = await this.query('SELECT COUNT(*) as count FROM users');
      const sessionCount = await this.query('SELECT COUNT(*) as count FROM sessions');
      
      return {
        userCount: userCount.rows[0]?.count || 0,
        sessionCount: sessionCount.rows[0]?.count || 0,
        type: 'SQLite',
        path: this.dbPath
      };
    } catch (error) {
      logger.error('Error getting SQLite stats:', error);
      return null;
    }
  }

  /**
   * Close database connection
   */
  async close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            logger.error('Error closing SQLite database:', err);
          } else {
            logger.info('SQLite database connection closed');
          }
          this.isConnected = false;
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

module.exports = new SQLiteDatabase();
